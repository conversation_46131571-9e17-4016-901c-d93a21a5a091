/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5Canimted-Portfolio%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5Canimted-Portfolio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5Canimted-Portfolio%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5Canimted-Portfolio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9100\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\")), \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5Canimted-Portfolio%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5Canimted-Portfolio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(ssr)/./app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNteXBlcnNvbmFsJTIwcG9yZm9saW8lMjB3aGljaCUyMGklMjBjbG9uZSUyMGZyb20lMjBtZWVyJTIwaGFkaSU1QyU1Q2FuaW10ZWQtUG9ydGZvbGlvJTVDJTVDYXBwJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdJQUEySCIsInNvdXJjZXMiOlsid2VicGFjazovL215LXYwLXByb2plY3QvP2JlNjgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxteXBlcnNvbmFsIHBvcmZvbGlvIHdoaWNoIGkgY2xvbmUgZnJvbSBtZWVyIGhhZGlcXFxcYW5pbXRlZC1Qb3J0Zm9saW9cXFxcYXBwXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Portfolio)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var gsap__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! gsap */ \"(ssr)/./node_modules/gsap/index.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/value/use-scroll.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/value/use-transform.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Calendar_Code_Github_Globe_Mail_Palette_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Calendar,Code,Github,Globe,Mail,Palette,Star,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Calendar_Code_Github_Globe_Mail_Palette_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Calendar,Code,Github,Globe,Mail,Palette,Star,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Calendar_Code_Github_Globe_Mail_Palette_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Calendar,Code,Github,Globe,Mail,Palette,Star,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Calendar_Code_Github_Globe_Mail_Palette_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Calendar,Code,Github,Globe,Mail,Palette,Star,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Calendar_Code_Github_Globe_Mail_Palette_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Calendar,Code,Github,Globe,Mail,Palette,Star,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Calendar_Code_Github_Globe_Mail_Palette_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Calendar,Code,Github,Globe,Mail,Palette,Star,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Calendar_Code_Github_Globe_Mail_Palette_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Calendar,Code,Github,Globe,Mail,Palette,Star,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Calendar_Code_Github_Globe_Mail_Palette_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Calendar,Code,Github,Globe,Mail,Palette,Star,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Calendar_Code_Github_Globe_Mail_Palette_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Calendar,Code,Github,Globe,Mail,Palette,Star,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/palette.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Calendar_Code_Github_Globe_Mail_Palette_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Calendar,Code,Github,Globe,Mail,Palette,Star,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_Calendar_Code_Github_Globe_Mail_Palette_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,Calendar,Code,Github,Globe,Mail,Palette,Star,Users,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n// Register GSAP plugins\nif (false) {}\nfunction Portfolio() {\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const heroRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const aboutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const skillsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const projectsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [showHeader, setShowHeader] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const lastScrollY = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0);\n    const headerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [skillTab, setSkillTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // --- Custom blue glowing cursor for hero section ---\n    const [cursorPos, setCursorPos] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const [showHeroCursor, setShowHeroCursor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const heroCursorRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [trailPos, setTrailPos] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const [showHeroBlob, setShowHeroBlob] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const animationRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Splash cursor state for hero section\n    const [splashPos, setSplashPos] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const splashRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { scrollYProgress } = (0,framer_motion__WEBPACK_IMPORTED_MODULE_3__.useScroll)();\n    const backgroundY = (0,framer_motion__WEBPACK_IMPORTED_MODULE_4__.useTransform)(scrollYProgress, [\n        0,\n        1\n    ], [\n        \"0%\",\n        \"100%\"\n    ]);\n    const textY = (0,framer_motion__WEBPACK_IMPORTED_MODULE_4__.useTransform)(scrollYProgress, [\n        0,\n        1\n    ], [\n        \"0%\",\n        \"50%\"\n    ]);\n    const motivationalQuotes = [\n        \"Success is not final, failure is not fatal: It is the courage to continue that counts.\",\n        \"Dream big and dare to fail.\",\n        \"The only way to do great work is to love what you do.\",\n        \"Don't watch the clock; do what it does. Keep going.\",\n        \"Great things never come from comfort zones.\",\n        \"Believe you can and you're halfway there.\",\n        \"Push yourself, because no one else is going to do it for you.\",\n        \"Opportunities don't happen, you create them.\",\n        \"Don't stop when you're tired. Stop when you're done.\",\n        \"The harder you work for something, the greater you'll feel when you achieve it.\"\n    ];\n    const [quoteIndex, setQuoteIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Animated left-side scrolling texts\n    const leftMessages = [\n        \"Hi, welcome to my profile.\",\n        \"I'm a ctrl C + ctrl V engineer\",\n        \"Enjoy your stay!\",\n        \"Let's build something cool.\",\n        \"I debug by yelling at my screen.\",\n        \"Professional coffee drinker.\",\n        \"I turn caffeine into code.\",\n        \"My code works... on my machine.\",\n        \"I write bugs, then fix them for a living.\",\n        \"Stack Overflow is my best friend.\",\n        \"I can explain it to you, but I can't understand it for you.\",\n        \"I use dark mode even in daylight.\",\n        \"I break things just to fix them.\",\n        \"I'm not lazy, I'm on energy-saving mode.\",\n        \"I'm silently correcting your grammar.\",\n        \"I put the 'pro' in procrastinate.\"\n    ];\n    const [leftMsgIndex, setLeftMsgIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const interval = setInterval(()=>{\n            setLeftMsgIndex((prev)=>(prev + 1) % leftMessages.length);\n        }, 2500);\n        return ()=>clearInterval(interval);\n    }, [\n        leftMessages.length\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const interval = setInterval(()=>{\n            let nextIndex;\n            do {\n                nextIndex = Math.floor(Math.random() * motivationalQuotes.length);\n            }while (nextIndex === quoteIndex);\n            setQuoteIndex(nextIndex);\n        }, 4000);\n        return ()=>clearInterval(interval);\n    }, [\n        quoteIndex,\n        motivationalQuotes.length\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const ctx = gsap__WEBPACK_IMPORTED_MODULE_5__.gsap.context(()=>{\n            // Hero animations\n            gsap__WEBPACK_IMPORTED_MODULE_5__.gsap.fromTo(\".hero-title\", {\n                y: 100,\n                opacity: 0\n            }, {\n                y: 0,\n                opacity: 1,\n                duration: 1.5,\n                ease: \"power3.out\"\n            });\n            gsap__WEBPACK_IMPORTED_MODULE_5__.gsap.fromTo(\".hero-subtitle\", {\n                y: 50,\n                opacity: 0\n            }, {\n                y: 0,\n                opacity: 1,\n                duration: 1.2,\n                delay: 0.3,\n                ease: \"power3.out\"\n            });\n            // Parallax background\n            gsap__WEBPACK_IMPORTED_MODULE_5__.gsap.to(\".parallax-bg\", {\n                yPercent: -50,\n                ease: \"none\",\n                scrollTrigger: {\n                    trigger: \".parallax-bg\",\n                    start: \"top bottom\",\n                    end: \"bottom top\",\n                    scrub: true\n                }\n            });\n            // About section animations\n            gsap__WEBPACK_IMPORTED_MODULE_5__.gsap.fromTo(\".about-text\", {\n                x: -100,\n                opacity: 0\n            }, {\n                x: 0,\n                opacity: 1,\n                duration: 1,\n                scrollTrigger: {\n                    trigger: \".about-section\",\n                    start: \"top 80%\",\n                    end: \"bottom 20%\",\n                    toggleActions: \"play none none reverse\"\n                }\n            });\n            // Skills sticky animation\n            gsap__WEBPACK_IMPORTED_MODULE_5__.gsap.to(\".skills-progress\", {\n                width: \"100%\",\n                duration: 2,\n                ease: \"power2.out\",\n                scrollTrigger: {\n                    trigger: \".skills-section\",\n                    start: \"top 60%\",\n                    end: \"bottom 40%\",\n                    toggleActions: \"play none none reverse\"\n                }\n            });\n            // Projects scroll animation\n            gsap__WEBPACK_IMPORTED_MODULE_5__.gsap.fromTo(\".project-card\", {\n                y: 100,\n                opacity: 0,\n                scale: 0.8\n            }, {\n                y: 0,\n                opacity: 1,\n                scale: 1,\n                duration: 0.8,\n                stagger: 0.2,\n                ease: \"back.out(1.7)\",\n                scrollTrigger: {\n                    trigger: \".projects-section\",\n                    start: \"top 70%\",\n                    end: \"bottom 30%\",\n                    toggleActions: \"play none none reverse\"\n                }\n            });\n            // Floating elements\n            gsap__WEBPACK_IMPORTED_MODULE_5__.gsap.to(\".floating-element\", {\n                y: -20,\n                duration: 2,\n                repeat: -1,\n                yoyo: true,\n                ease: \"power2.inOut\",\n                stagger: 0.5\n            });\n            // White dots animation\n            gsap__WEBPACK_IMPORTED_MODULE_5__.gsap.to(\".white-dot\", {\n                y: -30,\n                x: 15,\n                duration: 3,\n                repeat: -1,\n                yoyo: true,\n                ease: \"power2.inOut\",\n                stagger: 0.3\n            });\n        }, containerRef);\n        return ()=>ctx.revert();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleScroll = ()=>{\n            if (window.scrollY > lastScrollY.current && window.scrollY > 80) {\n                setShowHeader(false);\n            } else {\n                setShowHeader(true);\n            }\n            lastScrollY.current = window.scrollY;\n        };\n        window.addEventListener(\"scroll\", handleScroll);\n        return ()=>window.removeEventListener(\"scroll\", handleScroll);\n    }, []);\n    // Show header on mouse enter at the top\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleMouseMove = (e)=>{\n            if (e.clientY < 80) setShowHeader(true);\n        };\n        window.addEventListener(\"mousemove\", handleMouseMove);\n        return ()=>window.removeEventListener(\"mousemove\", handleMouseMove);\n    }, []);\n    // Smooth trailing animation for the abstract blob\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!showHeroBlob) return;\n        const lerp = (a, b, n)=>a + (b - a) * n;\n        const animate = ()=>{\n            setTrailPos((prev)=>{\n                const x = lerp(prev.x, cursorPos.x, 0.13);\n                const y = lerp(prev.y, cursorPos.y, 0.13);\n                return {\n                    x,\n                    y\n                };\n            });\n            animationRef.current = requestAnimationFrame(animate);\n        };\n        animationRef.current = requestAnimationFrame(animate);\n        return ()=>{\n            if (animationRef.current) cancelAnimationFrame(animationRef.current);\n        };\n    }, [\n        cursorPos,\n        showHeroBlob\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleMouseMove = (e)=>{\n            const hero = heroRef.current;\n            if (!hero) return;\n            const rect = hero.getBoundingClientRect();\n            setSplashPos({\n                x: e.clientX - rect.left,\n                y: e.clientY - rect.top\n            });\n        };\n        const hero = heroRef.current;\n        if (hero) hero.addEventListener(\"mousemove\", handleMouseMove);\n        if (hero) hero.addEventListener(\"mouseleave\", ()=>setSplashPos(null));\n        return ()=>{\n            if (hero) hero.removeEventListener(\"mousemove\", handleMouseMove);\n            if (hero) hero.removeEventListener(\"mouseleave\", ()=>setSplashPos(null));\n        };\n    }, []);\n    const [expTab, setExpTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const expTabs = [\n        {\n            title: \"EXPERIENCE\",\n            stats: [\n                {\n                    icon: _barrel_optimize_names_ArrowRight_Award_Calendar_Code_Github_Globe_Mail_Palette_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                    number: \"1+\",\n                    label: \"YEARS EXPERIENCE\"\n                },\n                {\n                    icon: _barrel_optimize_names_ArrowRight_Award_Calendar_Code_Github_Globe_Mail_Palette_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                    number: \"10+\",\n                    label: \"PROJECTS COMPLETED\"\n                },\n                {\n                    icon: _barrel_optimize_names_ArrowRight_Award_Calendar_Code_Github_Globe_Mail_Palette_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                    number: \"7+\",\n                    label: \"HAPPY CLIENTS\"\n                },\n                {\n                    icon: _barrel_optimize_names_ArrowRight_Award_Calendar_Code_Github_Globe_Mail_Palette_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                    number: \"1+\",\n                    label: \"AWARDS WON\"\n                }\n            ]\n        },\n        {\n            title: \"EDUCATION\",\n            stats: [\n                {\n                    icon: _barrel_optimize_names_ArrowRight_Award_Calendar_Code_Github_Globe_Mail_Palette_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                    number: \"2025\",\n                    label: \"GRADUATED\"\n                },\n                {\n                    icon: _barrel_optimize_names_ArrowRight_Award_Calendar_Code_Github_Globe_Mail_Palette_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                    number: \"BSc\",\n                    label: \"COMPUTER SCIENCE\"\n                },\n                {\n                    icon: _barrel_optimize_names_ArrowRight_Award_Calendar_Code_Github_Globe_Mail_Palette_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                    number: \"3.8\",\n                    label: \"GPA\"\n                },\n                {\n                    icon: _barrel_optimize_names_ArrowRight_Award_Calendar_Code_Github_Globe_Mail_Palette_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                    number: \"2\",\n                    label: \"HONORS\"\n                }\n            ]\n        },\n        {\n            title: \"CERTIFICATION\",\n            stats: [\n                {\n                    icon: _barrel_optimize_names_ArrowRight_Award_Calendar_Code_Github_Globe_Mail_Palette_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                    number: \"5+\",\n                    label: \"CERTIFICATES\"\n                },\n                {\n                    icon: _barrel_optimize_names_ArrowRight_Award_Calendar_Code_Github_Globe_Mail_Palette_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                    number: \"3\",\n                    label: \"ONLINE COURSES\"\n                },\n                {\n                    icon: _barrel_optimize_names_ArrowRight_Award_Calendar_Code_Github_Globe_Mail_Palette_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                    number: \"2\",\n                    label: \"BOOTCAMPS\"\n                },\n                {\n                    icon: _barrel_optimize_names_ArrowRight_Award_Calendar_Code_Github_Globe_Mail_Palette_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                    number: \"2025\",\n                    label: \"LAST UPDATED\"\n                }\n            ]\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        className: \"bg-black text-white overflow-x-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                ref: headerRef,\n                className: `fixed top-6 left-1/2 -translate-x-1/2 z-50 w-[90vw] max-w-5xl rounded-3xl bg-white/10 backdrop-blur-md shadow-lg flex items-center justify-between px-8 py-4 transition-transform duration-500 ${showHeader ? \"translate-y-0 opacity-100\" : \"-translate-y-32 opacity-0 pointer-events-none\"}`,\n                onMouseEnter: ()=>setShowHeader(true),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: \"/hijabi-girl-aesthetic.jpg\",\n                                alt: \"Logo\",\n                                className: \"w-10 h-10 rounded-2xl object-cover\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 307,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-black text-2xl tracking-tight text-white/90\",\n                                children: \"My Profile\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                        lineNumber: 306,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"flex gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"#about\",\n                                className: \"text-white/80 font-semibold hover:text-white transition-colors px-3 py-1 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400\",\n                                children: \"About\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 311,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"#skills\",\n                                className: \"text-white/80 font-semibold hover:text-white transition-colors px-3 py-1 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400\",\n                                children: \"Skills\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"#projects\",\n                                className: \"text-white/80 font-semibold hover:text-white transition-colors px-3 py-1 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400\",\n                                children: \"Projects\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"#testimonials\",\n                                className: \"text-white/80 font-semibold hover:text-white transition-colors px-3 py-1 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400\",\n                                children: \"Testimonials\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 314,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"#contact\",\n                                className: \"text-white/80 font-semibold hover:text-white transition-colors px-3 py-1 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-400\",\n                                children: \"Contact\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                        lineNumber: 310,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                lineNumber: 301,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                ref: heroRef,\n                className: \"relative h-screen flex items-center justify-center overflow-hidden pt-32 pb-32 px-4 md:px-8 lg:px-16\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 z-0 pointer-events-none\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-1/4 left-1/5 w-60 h-60 bg-gradient-to-br from-purple-500 via-blue-500 to-pink-400 opacity-20 rounded-full blur-xl animate-blob1\",\n                                style: {\n                                    willChange: \"transform\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 327,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-2/3 right-1/4 w-44 h-44 bg-gradient-to-br from-blue-400 via-purple-400 to-pink-500 opacity-15 rounded-full blur-xl animate-blob2\",\n                                style: {\n                                    willChange: \"transform\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 11\n                            }, this),\n                            Array.from({\n                                length: 3\n                            }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute bg-white rounded-full opacity-30 animate-float-dot\",\n                                    style: {\n                                        width: `${Math.random() * 10 + 8}px`,\n                                        height: `${Math.random() * 10 + 8}px`,\n                                        top: `${Math.random() * 80 + 10}%`,\n                                        left: `${Math.random() * 80 + 10}%`,\n                                        animationDelay: `${Math.random() * 3}s`,\n                                        willChange: \"transform\"\n                                    }\n                                }, i, false, {\n                                    fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                    lineNumber: 331,\n                                    columnNumber: 13\n                                }, this))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                        lineNumber: 325,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute left-0 top-1/2 -translate-y-1/2 z-30 w-64 flex flex-col items-start pointer-events-none select-none\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                            initial: {\n                                y: 40,\n                                opacity: 0\n                            },\n                            animate: {\n                                y: 0,\n                                opacity: 1\n                            },\n                            exit: {\n                                y: -40,\n                                opacity: 0\n                            },\n                            transition: {\n                                duration: 0.7\n                            },\n                            className: \"text-lg md:text-xl font-bold text-white/80 bg-black/30 px-5 py-3 rounded-r-2xl shadow-lg\",\n                            style: {\n                                minWidth: \"200px\"\n                            },\n                            children: leftMessages[leftMsgIndex]\n                        }, leftMsgIndex, false, {\n                            fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                            lineNumber: 347,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                        lineNumber: 346,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                        className: \"parallax-bg absolute inset-0 z-0\",\n                        style: {\n                            y: backgroundY\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-br from-slate-900 via-purple-900/30 to-black\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 360,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-[radial-gradient(circle_at_30%_40%,rgba(120,119,198,0.15),transparent_50%)]\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 361,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                        lineNumber: 359,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                        className: \"relative z-10 text-center px-4\",\n                        style: {\n                            y: textY\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.h1, {\n                                className: \"hero-title text-6xl md:text-8xl lg:text-9xl font-black tracking-tighter mb-6\",\n                                initial: {\n                                    opacity: 0,\n                                    y: 60\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 1.2,\n                                    ease: \"easeOut\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"shiny-text\",\n                                        children: \"Iqra\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                        lineNumber: 372,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"shiny-text\",\n                                        children: \"Urooj\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 365,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"hero-subtitle text-xl md:text-2xl font-bold tracking-wide text-gray-300 mb-8\",\n                                children: \"Agentic AI & Full Stack Developer\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 375,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-6 justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.a, {\n                                        href: \"#projects\",\n                                        className: \"px-8 py-4 bg-white text-black font-bold text-lg tracking-wide hover:bg-gray-200 transition-colors\",\n                                        whileHover: {\n                                            scale: 1.05\n                                        },\n                                        whileTap: {\n                                            scale: 0.95\n                                        },\n                                        children: \"VIEW WORK\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                        lineNumber: 379,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/contact\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                            className: \"px-8 py-4 border-2 border-white text-white font-bold text-lg tracking-wide hover:bg-white hover:text-black transition-colors cursor-pointer\",\n                                            whileHover: {\n                                                scale: 1.05\n                                            },\n                                            whileTap: {\n                                                scale: 0.95\n                                            },\n                                            children: \"CONTACT\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                            lineNumber: 388,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                        lineNumber: 387,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 378,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                        lineNumber: 364,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"floating-element absolute top-20 left-20 w-4 h-4 bg-purple-500 rounded-full\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                        lineNumber: 400,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"floating-element absolute top-40 right-32 w-6 h-6 bg-blue-500 rounded-full\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                        lineNumber: 401,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"floating-element absolute bottom-32 left-1/4 w-3 h-3 bg-pink-500 rounded-full\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                        lineNumber: 402,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            x: 40,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            x: 0,\n                            y: 0\n                        },\n                        exit: {\n                            opacity: 0,\n                            x: 40,\n                            y: 20\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        className: \"absolute bottom-10 right-10 z-20 max-w-md rounded-2xl px-6 py-4 shadow-lg flex items-center gap-2\",\n                        style: {\n                            minWidth: \"260px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-3xl md:text-4xl text-white/80 font-bold italic drop-shadow-lg\",\n                                children: '\"'\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 414,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-base md:text-lg text-white/90 font-medium text-left px-2\",\n                                children: motivationalQuotes[quoteIndex]\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 415,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-3xl md:text-4xl text-white/80 font-bold italic drop-shadow-lg\",\n                                children: '\"'\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 416,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, quoteIndex, true, {\n                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                        lineNumber: 405,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                lineNumber: 319,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"about\",\n                ref: aboutRef,\n                className: \"about-section pt-32 pb-32 px-4 md:px-8 lg:px-16 relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"white-dot absolute top-10 left-10 w-2 h-2 bg-white rounded-full opacity-60\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 423,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"white-dot absolute top-32 right-24 w-3 h-3 bg-white rounded-full opacity-40\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 424,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"white-dot absolute bottom-24 left-1/3 w-1 h-1 bg-white rounded-full opacity-80\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 425,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"white-dot absolute top-1/2 left-1/2 w-2 h-2 bg-white rounded-full opacity-50\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 426,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"white-dot absolute bottom-10 right-1/4 w-3 h-3 bg-white rounded-full opacity-30\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 427,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"white-dot absolute top-1/4 right-1/3 w-1 h-1 bg-white rounded-full opacity-70\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 428,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"white-dot absolute bottom-1/3 left-1/4 w-2 h-2 bg-white rounded-full opacity-60\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 429,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"white-dot absolute top-3/4 right-10 w-1 h-1 bg-white rounded-full opacity-90\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 430,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                        lineNumber: 422,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-6xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.h2, {\n                                className: \"text-5xl md:text-7xl font-black tracking-tighter mb-16 text-center\",\n                                initial: {\n                                    opacity: 0,\n                                    y: 50\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: \"ABOUT ME\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 433,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid md:grid-cols-2 gap-16 items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"about-text\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xl md:text-2xl leading-relaxed text-gray-300 mb-8\",\n                                                children: \"I'm a passionate creative developer who loves crafting digital experiences that push boundaries and inspire users.\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                                lineNumber: 445,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-lg leading-relaxed text-gray-400 mb-8\",\n                                                children: \"With expertise in modern web technologies, I specialize in creating immersive, interactive websites that tell stories and engage audiences through motion and design.\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                                lineNumber: 449,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-6 mb-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.a, {\n                                                        href: \"https://github.com/uroojiqra58gmailcom\",\n                                                        target: \"_blank\",\n                                                        rel: \"noopener noreferrer\",\n                                                        className: \"text-white hover:text-purple-400 transition-colors\",\n                                                        whileHover: {\n                                                            scale: 1.2\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Calendar_Code_Github_Globe_Mail_Palette_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            size: 32\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                                            lineNumber: 461,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                                        lineNumber: 454,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.a, {\n                                                        href: \"mailto:<EMAIL>\",\n                                                        className: \"text-white hover:text-green-400 transition-colors\",\n                                                        whileHover: {\n                                                            scale: 1.2\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Calendar_Code_Github_Globe_Mail_Palette_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            size: 32\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                                            lineNumber: 468,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                                        lineNumber: 463,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                                lineNumber: 453,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/cv.pdf\",\n                                                target: \"_blank\",\n                                                rel: \"noopener noreferrer\",\n                                                className: \"inline-flex items-center gap-2 px-6 py-3 rounded-full bg-gradient-to-r from-purple-500 to-blue-500 text-white font-bold shadow-md hover:from-purple-600 hover:to-blue-600 transition-colors text-lg mt-2\",\n                                                download: true,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        fill: \"none\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        strokeWidth: 2,\n                                                        stroke: \"currentColor\",\n                                                        className: \"w-6 h-6\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            d: \"M12 4v16m0 0l-6-6m6 6l6-6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                                            lineNumber: 479,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                                        lineNumber: 478,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"My CV\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                                lineNumber: 471,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                        lineNumber: 444,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                        className: \"relative\",\n                                        initial: {\n                                            opacity: 0,\n                                            scale: 0.8\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            scale: 1\n                                        },\n                                        transition: {\n                                            duration: 0.8\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-[400px] h-[520px] mx-auto bg-gradient-to-br from-purple-600 to-blue-600 rounded-[2.5rem] flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-[380px] h-[500px] bg-black rounded-[2.5rem] flex items-center justify-center overflow-hidden\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: \"/hijabi-girl-aesthetic.jpg\",\n                                                    alt: \"Iqra Urooj\",\n                                                    className: \"w-[380px] h-[500px] object-cover rounded-[2.5rem]\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                                    lineNumber: 494,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                                lineNumber: 493,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                            lineNumber: 492,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                        lineNumber: 485,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 443,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                        lineNumber: 432,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                lineNumber: 421,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"pt-10 pb-10 px-4 md:px-8 lg:px-16 text-white relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 z-0 zebra-bg pointer-events-none\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                        lineNumber: 504,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-6xl mx-auto relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.h2, {\n                                className: \"text-5xl md:text-7xl font-black tracking-tighter mb-16 text-center text-white\",\n                                initial: {\n                                    opacity: 0,\n                                    y: 50\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: expTabs[expTab].title\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 506,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                                children: expTabs[expTab].stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                        className: \"text-center\",\n                                        initial: {\n                                            opacity: 0,\n                                            y: 50\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.6,\n                                            delay: index * 0.1\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(stat.icon, {\n                                                    size: 48,\n                                                    className: \"mx-auto text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                                    lineNumber: 526,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                                lineNumber: 525,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-4xl font-black text-white mb-2\",\n                                                children: stat.number\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                                lineNumber: 528,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-white font-bold tracking-wide\",\n                                                children: stat.label\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                                lineNumber: 529,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, stat.label, true, {\n                                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                        lineNumber: 517,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 515,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center mt-10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"w-14 h-14 rounded-full bg-white flex items-center justify-center shadow-lg hover:bg-gray-200 transition-colors\",\n                                    style: {\n                                        border: \"none\"\n                                    },\n                                    onClick: ()=>setExpTab((expTab + 1) % expTabs.length),\n                                    \"aria-label\": \"Swipe\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Calendar_Code_Github_Globe_Mail_Palette_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"text-black w-8 h-8\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                        lineNumber: 541,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                    lineNumber: 535,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 534,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                        lineNumber: 505,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                lineNumber: 503,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"skills\",\n                className: \"skills-section pt-32 pb-48 px-4 md:px-8 lg:px-16 relative overflow-hidden bg-black\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"white-dot absolute top-12 left-16 w-2 h-2 bg-white rounded-full opacity-60\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 550,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"white-dot absolute top-36 right-28 w-3 h-3 bg-white rounded-full opacity-40\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 551,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"white-dot absolute bottom-28 left-1/2 w-1 h-1 bg-white rounded-full opacity-80\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 552,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"white-dot absolute top-2/3 left-1/3 w-2 h-2 bg-white rounded-full opacity-50\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 553,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"white-dot absolute bottom-12 right-1/5 w-3 h-3 bg-white rounded-full opacity-30\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 554,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"white-dot absolute top-1/3 right-1/2 w-1 h-1 bg-white rounded-full opacity-70\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 555,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"white-dot absolute bottom-1/4 left-1/5 w-2 h-2 bg-white rounded-full opacity-60\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 556,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"white-dot absolute top-3/5 right-16 w-1 h-1 bg-white rounded-full opacity-90\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 557,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                        lineNumber: 549,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-6xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.h2, {\n                                className: \"text-5xl md:text-7xl font-black tracking-tighter mb-10 text-center\",\n                                initial: {\n                                    opacity: 0,\n                                    y: 50\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: \"SKILLS\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 560,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center justify-center w-full mt-16\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full max-w-4xl px-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabSwitcher, {}, void 0, false, {\n                                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                        lineNumber: 572,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                    lineNumber: 571,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 570,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                        lineNumber: 559,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                lineNumber: 548,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"pt-32 pb-32 px-4 md:px-8 lg:px-16 relative overflow-hidden\",\n                style: {\n                    background: \"#141414\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 z-0 pointer-events-none\",\n                        children: Array.from({\n                            length: 12\n                        }).map((_, i)=>{\n                            // Randomize position, size, and border radius\n                            const top = Math.random() * 80 + 5; // 5% to 85%\n                            const left = Math.random() * 80 + 5;\n                            const width = Math.random() * 80 + 60; // 60px to 140px\n                            const height = Math.random() * 40 + 40; // 40px to 80px\n                            const borderRadius = Math.random() * 40 + 30; // 30px to 70px\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    position: \"absolute\",\n                                    top: `${top}%`,\n                                    left: `${left}%`,\n                                    width,\n                                    height,\n                                    background: \"#000\",\n                                    opacity: 0.18,\n                                    borderRadius: `${borderRadius}%`,\n                                    transform: `translate(-50%, -50%) rotate(${Math.random() * 360}deg)`\n                                }\n                            }, i, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 590,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                        lineNumber: 581,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-6xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.h2, {\n                                className: \"text-5xl md:text-7xl font-black tracking-tighter mb-16 text-center\",\n                                initial: {\n                                    opacity: 0,\n                                    y: 50\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: \"SERVICES\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 608,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid md:grid-cols-1 lg:grid-cols-1 gap-8 max-w-md mx-auto\",\n                                children: [\n                                    {\n                                        title: \"WEB DEVELOPMENT\",\n                                        description: \"Custom websites and web applications built with modern technologies like React, Bootstrap, and Tailwind CSS\"\n                                    }\n                                ].map((service, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                        className: \"bg-black p-8 rounded-lg border border-gray-800 hover:border-purple-500 transition-colors cursor-pointer hover:scale-[1.04] hover:-translate-y-2 hover:shadow-xl duration-300\",\n                                        initial: {\n                                            opacity: 0,\n                                            y: 50\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.6,\n                                            delay: index * 0.1\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        whileHover: {\n                                            y: -5\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-black tracking-wide mb-4 text-purple-400\",\n                                                children: service.title\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                                lineNumber: 634,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 mb-6 leading-relaxed\",\n                                                children: service.description\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                                lineNumber: 635,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, service.title, true, {\n                                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                        lineNumber: 625,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 618,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                        lineNumber: 607,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                lineNumber: 579,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"projects\",\n                ref: projectsRef,\n                className: \"projects-section pt-32 pb-32 px-4 md:px-8 lg:px-16 relative overflow-hidden bg-black\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                        lineNumber: 644,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"white-dot absolute top-20 left-20 w-2 h-2 bg-white rounded-full opacity-60\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 646,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"white-dot absolute top-40 right-32 w-3 h-3 bg-white rounded-full opacity-40\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 647,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"white-dot absolute bottom-32 left-1/4 w-1 h-1 bg-white rounded-full opacity-80\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 648,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"white-dot absolute top-60 left-1/2 w-2 h-2 bg-white rounded-full opacity-50\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 649,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"white-dot absolute bottom-40 right-1/4 w-3 h-3 bg-white rounded-full opacity-30\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 650,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"white-dot absolute top-32 right-1/3 w-1 h-1 bg-white rounded-full opacity-70\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 651,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"white-dot absolute bottom-60 left-1/3 w-2 h-2 bg-white rounded-full opacity-60\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 652,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"white-dot absolute top-80 right-20 w-1 h-1 bg-white rounded-full opacity-90\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 653,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                        lineNumber: 645,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-6xl mx-auto relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.h2, {\n                                className: \"text-5xl md:text-7xl font-black tracking-tighter mb-16 text-center\",\n                                initial: {\n                                    opacity: 0,\n                                    y: 50\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: \"PROJECTS\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 656,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                                children: [\n                                    {\n                                        title: \"SCIENTIFIC CALCULATOR\",\n                                        tech: \"JavaScript, CSS, HTML\",\n                                        image: \"/project.png\",\n                                        link: \"https://github.com/uroojiqra58gmailcom/Scientific-Calculator-\",\n                                        description: \"Advanced scientific calculator with modern UI and comprehensive mathematical functions\"\n                                    },\n                                    {\n                                        title: \"BOOTSTRAP CLONE WEBSITE\",\n                                        tech: \"Bootstrap, HTML, CSS\",\n                                        image: \"/project.png\",\n                                        link: \"https://github.com/uroojiqra58gmailcom/A-clone-website-with-the-help-of-bootstrap\",\n                                        description: \"Stunning responsive website clone built with Bootstrap featuring amazing modern design\"\n                                    },\n                                    {\n                                        title: \"3D ANIMATED LOGIN FORM\",\n                                        tech: \"CSS, HTML, JavaScript\",\n                                        image: \"/project.png\",\n                                        link: \"https://github.com/uroojiqra58gmailcom/3D-Animated-Login-form-\",\n                                        description: \"Eye-catching 3D animated login form with beautiful color combinations and smooth transitions\"\n                                    },\n                                    {\n                                        title: \"CGPA CALCULATOR\",\n                                        tech: \"JavaScript, CSS, HTML\",\n                                        image: \"/project.png\",\n                                        link: \"https://github.com/uroojiqra58gmailcom/Advance-Cgpa-Calculator\",\n                                        description: \"Advanced CGPA calculator with modern functionalities for academic grade management\"\n                                    },\n                                    {\n                                        title: \"SUDOKU SOLVER\",\n                                        tech: \"Python, Algorithms\",\n                                        image: \"/project.png\",\n                                        link: \"https://github.com/uroojiqra58gmailcom/Suduko-solver-\",\n                                        description: \"Intelligent Sudoku solver game with advanced algorithms and interactive gameplay\"\n                                    },\n                                    {\n                                        title: \"3D PLANETS PROJECT\",\n                                        tech: \"CSS, HTML, JavaScript\",\n                                        image: \"/project.png\",\n                                        link: \"https://github.com/uroojiqra58gmailcom/A-3D-Animated-project-about-planets\",\n                                        description: \"Interactive 3D planets project with hover effects and stunning visual animations\"\n                                    }\n                                ].map((project, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProjectCard, {\n                                        project: project,\n                                        index: index\n                                    }, project.title, false, {\n                                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                        lineNumber: 711,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 666,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                        lineNumber: 655,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                lineNumber: 643,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"testimonials\",\n                className: \"pt-32 pb-32 px-4 md:px-8 lg:px-16 relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 z-0 zebra-bg pointer-events-none\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                        lineNumber: 719,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-6xl mx-auto relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.h2, {\n                                className: \"text-5xl md:text-7xl font-black tracking-tighter mb-16 text-center\",\n                                initial: {\n                                    opacity: 0,\n                                    y: 50\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: \"TESTIMONIALS\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 721,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                                children: [\n                                    {\n                                        name: \"Sarah Johnson\",\n                                        role: \"CEO, TechStart\",\n                                        text: \"John delivered an exceptional website that exceeded our expectations. His attention to detail and creative vision is unmatched.\",\n                                        rating: 5\n                                    },\n                                    {\n                                        name: \"Mike Chen\",\n                                        role: \"Founder, DesignCo\",\n                                        text: \"Working with John was a game-changer for our business. The mobile app he built increased our user engagement by 300%.\",\n                                        rating: 5\n                                    },\n                                    {\n                                        name: \"Emily Davis\",\n                                        role: \"Marketing Director\",\n                                        text: \"Professional, creative, and reliable. John transformed our brand identity and created a stunning e-commerce platform.\",\n                                        rating: 5\n                                    }\n                                ].map((testimonial, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                        className: \"bg-black p-8 rounded-lg border border-gray-800 hover:border-purple-500 transition-colors hover-lift cursor-pointer\",\n                                        initial: {\n                                            opacity: 0,\n                                            y: 50\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.6,\n                                            delay: index * 0.1\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        whileHover: {\n                                            y: -8,\n                                            scale: 1.03\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex mb-4\",\n                                                children: [\n                                                    ...Array(testimonial.rating)\n                                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_Calendar_Code_Github_Globe_Mail_Palette_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        size: 20,\n                                                        className: \"text-yellow-400 fill-current\"\n                                                    }, i, false, {\n                                                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                                        lineNumber: 763,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                                lineNumber: 761,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-300 mb-6 leading-relaxed\",\n                                                children: [\n                                                    '\"',\n                                                    testimonial.text,\n                                                    '\"'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                                lineNumber: 766,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-bold text-white\",\n                                                        children: testimonial.name\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                                        lineNumber: 768,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-400 text-sm\",\n                                                        children: testimonial.role\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                                        lineNumber: 769,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                                lineNumber: 767,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, testimonial.name, true, {\n                                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                        lineNumber: 752,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 731,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                        lineNumber: 720,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                lineNumber: 718,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"contact\",\n                className: \"pt-32 pb-48 px-4 md:px-8 lg:px-16 bg-black relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"white-dot absolute top-20 left-20 w-2 h-2 bg-white rounded-full opacity-60\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 781,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"white-dot absolute top-40 right-32 w-3 h-3 bg-white rounded-full opacity-40\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 782,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"white-dot absolute bottom-32 left-1/4 w-1 h-1 bg-white rounded-full opacity-80\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 783,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"white-dot absolute top-60 left-1/2 w-2 h-2 bg-white rounded-full opacity-50\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 784,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"white-dot absolute bottom-40 right-1/4 w-3 h-3 bg-white rounded-full opacity-30\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 785,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"white-dot absolute top-32 right-1/3 w-1 h-1 bg-white rounded-full opacity-70\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 786,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"white-dot absolute bottom-60 left-1/3 w-2 h-2 bg-white rounded-full opacity-60\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 787,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"white-dot absolute top-80 right-20 w-1 h-1 bg-white rounded-full opacity-90\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 788,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                        lineNumber: 780,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-4xl mx-auto text-center relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.h2, {\n                                className: \"text-5xl md:text-7xl font-black tracking-tighter mb-8\",\n                                initial: {\n                                    opacity: 0,\n                                    y: 50\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: \"LET'S WORK\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 792,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.h2, {\n                                className: \"text-5xl md:text-7xl font-black tracking-tighter mb-16\",\n                                initial: {\n                                    opacity: 0,\n                                    y: 50\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8,\n                                    delay: 0.2\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: \"TOGETHER\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 801,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.p, {\n                                className: \"text-xl md:text-2xl text-purple-100 mb-12 max-w-2xl mx-auto\",\n                                initial: {\n                                    opacity: 0,\n                                    y: 30\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8,\n                                    delay: 0.4\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: \"Ready to bring your ideas to life? Let's create something amazing together.\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 811,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/contact\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                    className: \"inline-block jelly-green-btn cursor-pointer\",\n                                    initial: {\n                                        opacity: 0,\n                                        scale: 0.8\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        scale: 1\n                                    },\n                                    transition: {\n                                        duration: 0.8,\n                                        delay: 0.6\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: \"GET IN TOUCH\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                    lineNumber: 822,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 821,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                        lineNumber: 791,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                lineNumber: 778,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n        lineNumber: 299,\n        columnNumber: 5\n    }, this);\n}\n// TabSwitcher component for beautiful tabs and animated indicator\nfunction TabSwitcher() {\n    const [skillTab, setSkillTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const tabNames = [\n        \"Technical Skills\",\n        \"Soft Skills\",\n        \"Tools\"\n    ];\n    const tabRefs = [\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null),\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null),\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null)\n    ];\n    const indicatorRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [indicatorStyle, setIndicatorStyle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        left: 0,\n        width: 0\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(()=>{\n        const node = tabRefs[skillTab].current;\n        if (node) {\n            setIndicatorStyle({\n                left: node.offsetLeft,\n                width: node.offsetWidth\n            });\n        }\n    }, [\n        skillTab\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative flex flex-col items-center mb-14 w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex bg-white/10 backdrop-blur-md border border-white/20 rounded-full shadow-lg px-2 py-2 gap-2 relative min-w-[320px]\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: indicatorRef,\n                        className: \"absolute top-1 left-0 h-[calc(100%-0.5rem)] rounded-full bg-gradient-to-r from-purple-500/80 to-blue-500/80 shadow-lg transition-all duration-500 ease-[cubic-bezier(.4,2,.6,1)] z-0\",\n                        style: {\n                            left: indicatorStyle.left,\n                            width: indicatorStyle.width,\n                            pointerEvents: \"none\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                        lineNumber: 859,\n                        columnNumber: 9\n                    }, this),\n                    tabNames.map((tab, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            ref: tabRefs[idx],\n                            onClick: ()=>setSkillTab(idx),\n                            className: `relative z-10 px-7 py-2 font-bold font-mono rounded-full transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-purple-400\r\n              ${skillTab === idx ? \"text-white scale-105 bg-gradient-to-r from-purple-600/80 to-blue-600/80 shadow\" : \"text-gray-300 hover:text-white hover:scale-105\"}`,\n                            \"aria-selected\": skillTab === idx,\n                            tabIndex: 0,\n                            type: \"button\",\n                            children: tab\n                        }, tab, false, {\n                            fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                            lineNumber: 865,\n                            columnNumber: 11\n                        }, this))\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                lineNumber: 857,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full mt-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.AnimatePresence, {\n                    mode: \"wait\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabContent, {\n                        skillTab: skillTab\n                    }, skillTab, false, {\n                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                        lineNumber: 882,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                    lineNumber: 881,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                lineNumber: 880,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n        lineNumber: 856,\n        columnNumber: 5\n    }, this);\n}\n// TabContent component for animated tab panels\nfunction TabContent({ skillTab }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n        initial: {\n            opacity: 0,\n            y: 30\n        },\n        animate: {\n            opacity: 1,\n            y: 0\n        },\n        exit: {\n            opacity: 0,\n            y: -30\n        },\n        transition: {\n            duration: 0.5,\n            ease: [\n                0.4,\n                2,\n                0.6,\n                1\n            ]\n        },\n        children: [\n            skillTab === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-10 w-full\",\n                children: [\n                    {\n                        icon: _barrel_optimize_names_ArrowRight_Award_Calendar_Code_Github_Globe_Mail_Palette_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                        title: \"FRONTEND\",\n                        skills: [\n                            \"Bootstrap\",\n                            \"React\",\n                            \"Tailwind\"\n                        ]\n                    },\n                    {\n                        icon: _barrel_optimize_names_ArrowRight_Award_Calendar_Code_Github_Globe_Mail_Palette_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n                        title: \"DESIGN\",\n                        skills: [\n                            \"Figma\"\n                        ]\n                    },\n                    {\n                        icon: _barrel_optimize_names_ArrowRight_Award_Calendar_Code_Github_Globe_Mail_Palette_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                        title: \"ANIMATION\",\n                        skills: [\n                            \"CSS\"\n                        ]\n                    },\n                    {\n                        icon: _barrel_optimize_names_ArrowRight_Award_Calendar_Code_Github_Globe_Mail_Palette_Star_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n                        title: \"BACKEND\",\n                        skills: [\n                            \"Node.js\",\n                            \"Java\",\n                            \"MySQL\",\n                            \"MongoDB\"\n                        ]\n                    }\n                ].map((category, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                        className: \"text-center bg-black/80 rounded-2xl p-8 shadow-md hover:shadow-lg transition-all duration-300 w-full\",\n                        initial: {\n                            opacity: 0,\n                            y: 50\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.6,\n                            delay: index * 0.1\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(category.icon, {\n                                    size: 52,\n                                    className: \"mx-auto text-purple-400\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                    lineNumber: 916,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 915,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-black tracking-wide mb-4 text-white\",\n                                children: category.title\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 918,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: category.skills.map((skill)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-base text-gray-200 mb-1 font-semibold tracking-wide\",\n                                                children: skill\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                                lineNumber: 922,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full bg-gray-700 h-1.5 rounded-full\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"skills-progress h-1.5 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full\",\n                                                    style: {\n                                                        width: \"100%\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                                    lineNumber: 924,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                                lineNumber: 923,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, skill, true, {\n                                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                        lineNumber: 921,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                                lineNumber: 919,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, category.title, true, {\n                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                        lineNumber: 907,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                lineNumber: 900,\n                columnNumber: 9\n            }, this),\n            skillTab === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16\",\n                children: [\n                    \"Communication\",\n                    \"Problem Solving\",\n                    \"Teamwork\",\n                    \"Adaptability\",\n                    \"Creativity\",\n                    \"Critical Thinking\",\n                    \"Time Management\",\n                    \"Leadership\"\n                ].map((skill, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                        className: \"bg-black/80 border border-blue-600 rounded-2xl p-7 text-center text-white font-bold text-lg shadow-md transition-all duration-300 cursor-pointer hover:scale-[1.04] hover:-translate-y-2 hover:border-blue-400 hover:shadow-xl\",\n                        initial: {\n                            opacity: 0,\n                            y: 40\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.5,\n                            delay: index * 0.05\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        children: skill\n                    }, skill, false, {\n                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                        lineNumber: 948,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                lineNumber: 937,\n                columnNumber: 9\n            }, this),\n            skillTab === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid md:grid-cols-3 lg:grid-cols-3 gap-6 mb-8\",\n                children: [\n                    \"Git\",\n                    \"GitHub\",\n                    \"VS Code\"\n                ].map((tool, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                        className: \"bg-black/80 border border-purple-600 rounded-2xl p-5 text-center text-white font-semibold text-base shadow-md transition-all duration-300 cursor-pointer hover:scale-[1.04] hover:-translate-y-2 hover:border-purple-400 hover:shadow-xl\",\n                        initial: {\n                            opacity: 0,\n                            y: 30\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.4,\n                            delay: index * 0.03\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        children: tool\n                    }, tool, false, {\n                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                        lineNumber: 968,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                lineNumber: 962,\n                columnNumber: 9\n            }, this)\n        ]\n    }, skillTab, true, {\n        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n        lineNumber: 892,\n        columnNumber: 5\n    }, this);\n}\nfunction ProjectCard({ project, index }) {\n    const cardRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [tilt, setTilt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    function handleMouseMove(e) {\n        const card = cardRef.current;\n        if (!card) return;\n        const rect = card.getBoundingClientRect();\n        const x = e.clientX - rect.left;\n        const y = e.clientY - rect.top;\n        const centerX = rect.width / 2;\n        const centerY = rect.height / 2;\n        // Max tilt angle\n        const maxTilt = 15;\n        const tiltX = (y - centerY) / centerY * maxTilt;\n        const tiltY = (x - centerX) / centerX * maxTilt;\n        setTilt({\n            x: tiltX,\n            y: tiltY\n        });\n    }\n    function handleMouseLeave() {\n        setTilt({\n            x: 0,\n            y: 0\n        });\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n        ref: cardRef,\n        className: \"group relative flex flex-col items-center bg-gradient-to-br from-white/5 to-black/60 border border-gray-800 rounded-2xl shadow-xl px-8 pt-8 pb-6 transition-all duration-300 hover:shadow-[0_8px_40px_0_rgba(162,89,247,0.18)] hover:-translate-y-2 hover:scale-[1.04] cursor-pointer min-h-[420px]\",\n        initial: {\n            opacity: 0,\n            y: 50\n        },\n        whileInView: {\n            opacity: 1,\n            y: 0\n        },\n        transition: {\n            duration: 0.6,\n            delay: index * 0.08\n        },\n        viewport: {\n            once: true\n        },\n        style: {\n            transform: `perspective(900px) rotateX(${tilt.x}deg) rotateY(${tilt.y}deg) scale(1)`,\n            willChange: \"transform\"\n        },\n        onMouseMove: handleMouseMove,\n        onMouseLeave: handleMouseLeave,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full flex justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-28 h-28 rounded-xl overflow-hidden border-4 border-white/10 bg-gradient-to-br from-purple-700/80 to-blue-700/80 shadow-lg group-hover:scale-105 transition-transform duration-500\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: project.image || \"/placeholder.svg\",\n                        alt: project.title,\n                        className: \"w-full h-full object-cover object-center\",\n                        style: {\n                            aspectRatio: \"1/1\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                        lineNumber: 1025,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                    lineNumber: 1024,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                lineNumber: 1023,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col items-center text-center w-full mt-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-extrabold tracking-tight mb-2 text-white drop-shadow-lg leading-tight min-h-[56px] flex items-center justify-center\",\n                        children: project.title\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                        lineNumber: 1034,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-300 text-base mb-6 font-mono\",\n                        children: project.tech\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                        lineNumber: 1035,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: project.link || \"#\",\n                        target: \"_blank\",\n                        rel: \"noopener noreferrer\",\n                        className: \"w-full mt-auto px-0 py-3 rounded-xl bg-gradient-to-r from-purple-500 to-blue-500 text-white font-bold text-base shadow-md hover:from-purple-600 hover:to-blue-600 transition-colors focus:outline-none focus:ring-2 focus:ring-purple-400 active:scale-95 block text-center\",\n                        children: \"View on GitHub\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                        lineNumber: 1036,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n                lineNumber: 1033,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\page.tsx\",\n        lineNumber: 1009,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"8aba33dda0fa\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9teS12MC1wcm9qZWN0Ly4vYXBwL2dsb2JhbHMuY3NzP2RkYTciXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI4YWJhMzNkZGEwZmFcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"Your Name - Portfolio\",\n    description: \"Portfolio of John Doe - Creative Developer & Designer\",\n    generator: \"v0.dev\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"dark\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                    href: \"https://fonts.googleapis.com/css2?family=Montserrat:wght@700;900&display=swap\",\n                    rel: \"stylesheet\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\layout.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\layout.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className)} min-h-screen flex flex-col`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"flex-1\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\layout.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\layout.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\layout.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\mypersonal porfolio which i clone from meer hadi\animted-Portfolio\app\page.tsx#default`));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/framer-motion","vendor-chunks/motion-dom","vendor-chunks/lucide-react","vendor-chunks/motion-utils","vendor-chunks/@swc","vendor-chunks/gsap"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5Canimted-Portfolio%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5Canimted-Portfolio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();