[{"name":"hot-reloader","duration":81,"timestamp":2660935267310,"id":3,"tags":{"version":"14.2.16","isTurbopack":false},"startTime":1753619878075,"traceId":"9380624929474dbc"},{"name":"start","duration":3,"timestamp":2660935268468,"id":4,"parentId":3,"tags":{},"startTime":1753619878076,"traceId":"9380624929474dbc"},{"name":"get-version-info","duration":691704,"timestamp":2660935268826,"id":5,"parentId":4,"tags":{},"startTime":1753619878076,"traceId":"9380624929474dbc"},{"name":"clean","duration":89209,"timestamp":2660935960602,"id":6,"parentId":4,"tags":{},"startTime":1753619878768,"traceId":"9380624929474dbc"},{"name":"create-pages-mapping","duration":331,"timestamp":2660936051992,"id":8,"parentId":7,"tags":{},"startTime":1753619878860,"traceId":"9380624929474dbc"},{"name":"create-entrypoints","duration":165774,"timestamp":2660936052372,"id":9,"parentId":7,"tags":{},"startTime":1753619878860,"traceId":"9380624929474dbc"},{"name":"generate-webpack-config","duration":576405,"timestamp":2660936218381,"id":10,"parentId":7,"tags":{},"startTime":1753619879026,"traceId":"9380624929474dbc"},{"name":"get-webpack-config","duration":743009,"timestamp":2660936051821,"id":7,"parentId":4,"tags":{},"startTime":1753619878859,"traceId":"9380624929474dbc"},{"name":"make","duration":2408,"timestamp":2660937033564,"id":12,"parentId":11,"tags":{},"startTime":1753619879841,"traceId":"9380624929474dbc"},{"name":"chunk-graph","duration":1556,"timestamp":2660937044381,"id":14,"parentId":13,"tags":{},"startTime":1753619879852,"traceId":"9380624929474dbc"},{"name":"optimize-modules","duration":41,"timestamp":2660937046198,"id":16,"parentId":13,"tags":{},"startTime":1753619879854,"traceId":"9380624929474dbc"},{"name":"optimize-chunks","duration":454,"timestamp":2660937046522,"id":17,"parentId":13,"tags":{},"startTime":1753619879854,"traceId":"9380624929474dbc"},{"name":"optimize-tree","duration":255,"timestamp":2660937047112,"id":18,"parentId":13,"tags":{},"startTime":1753619879855,"traceId":"9380624929474dbc"},{"name":"optimize-chunk-modules","duration":258,"timestamp":2660937047745,"id":19,"parentId":13,"tags":{},"startTime":1753619879855,"traceId":"9380624929474dbc"},{"name":"optimize","duration":2177,"timestamp":2660937046105,"id":15,"parentId":13,"tags":{},"startTime":1753619879854,"traceId":"9380624929474dbc"},{"name":"module-hash","duration":170,"timestamp":2660937050028,"id":20,"parentId":13,"tags":{},"startTime":1753619879858,"traceId":"9380624929474dbc"},{"name":"code-generation","duration":432,"timestamp":2660937050250,"id":21,"parentId":13,"tags":{},"startTime":1753619879858,"traceId":"9380624929474dbc"},{"name":"hash","duration":1119,"timestamp":2660937051157,"id":22,"parentId":13,"tags":{},"startTime":1753619879859,"traceId":"9380624929474dbc"},{"name":"code-generation-jobs","duration":328,"timestamp":2660937052269,"id":23,"parentId":13,"tags":{},"startTime":1753619879860,"traceId":"9380624929474dbc"},{"name":"module-assets","duration":184,"timestamp":2660937052535,"id":24,"parentId":13,"tags":{},"startTime":1753619879860,"traceId":"9380624929474dbc"},{"name":"create-chunk-assets","duration":423,"timestamp":2660937052741,"id":25,"parentId":13,"tags":{},"startTime":1753619879860,"traceId":"9380624929474dbc"},{"name":"NextJsBuildManifest-generateClientManifest","duration":1968,"timestamp":2660938152257,"id":27,"parentId":11,"tags":{},"startTime":1753619880960,"traceId":"9380624929474dbc"},{"name":"NextJsBuildManifest-createassets","duration":2897,"timestamp":2660938151420,"id":26,"parentId":11,"tags":{},"startTime":1753619880959,"traceId":"9380624929474dbc"},{"name":"seal","duration":1115408,"timestamp":2660937043581,"id":13,"parentId":11,"tags":{},"startTime":1753619879851,"traceId":"9380624929474dbc"},{"name":"webpack-compilation","duration":1143189,"timestamp":2660937016509,"id":11,"parentId":3,"tags":{"name":"client"},"startTime":1753619879824,"traceId":"9380624929474dbc"},{"name":"emit","duration":160787,"timestamp":2660938160469,"id":28,"parentId":3,"tags":{},"startTime":1753619880968,"traceId":"9380624929474dbc"},{"name":"make","duration":6367,"timestamp":2660938351405,"id":30,"parentId":29,"tags":{},"startTime":1753619881159,"traceId":"9380624929474dbc"},{"name":"chunk-graph","duration":57,"timestamp":2660938359035,"id":32,"parentId":31,"tags":{},"startTime":1753619881167,"traceId":"9380624929474dbc"},{"name":"optimize-modules","duration":10,"timestamp":2660938359132,"id":34,"parentId":31,"tags":{},"startTime":1753619881167,"traceId":"9380624929474dbc"},{"name":"optimize-chunks","duration":20009,"timestamp":2660938359259,"id":35,"parentId":31,"tags":{},"startTime":1753619881167,"traceId":"9380624929474dbc"},{"name":"optimize-tree","duration":18,"timestamp":2660938379403,"id":36,"parentId":31,"tags":{},"startTime":1753619881187,"traceId":"9380624929474dbc"},{"name":"optimize-chunk-modules","duration":13,"timestamp":2660938379546,"id":37,"parentId":31,"tags":{},"startTime":1753619881187,"traceId":"9380624929474dbc"},{"name":"optimize","duration":20564,"timestamp":2660938359121,"id":33,"parentId":31,"tags":{},"startTime":1753619881167,"traceId":"9380624929474dbc"},{"name":"module-hash","duration":17,"timestamp":2660938379960,"id":38,"parentId":31,"tags":{},"startTime":1753619881188,"traceId":"9380624929474dbc"},{"name":"code-generation","duration":12,"timestamp":2660938379993,"id":39,"parentId":31,"tags":{},"startTime":1753619881188,"traceId":"9380624929474dbc"},{"name":"hash","duration":126,"timestamp":2660938380072,"id":40,"parentId":31,"tags":{},"startTime":1753619881188,"traceId":"9380624929474dbc"},{"name":"code-generation-jobs","duration":73,"timestamp":2660938380198,"id":41,"parentId":31,"tags":{},"startTime":1753619881188,"traceId":"9380624929474dbc"},{"name":"module-assets","duration":21,"timestamp":2660938380257,"id":42,"parentId":31,"tags":{},"startTime":1753619881188,"traceId":"9380624929474dbc"},{"name":"create-chunk-assets","duration":26,"timestamp":2660938380288,"id":43,"parentId":31,"tags":{},"startTime":1753619881188,"traceId":"9380624929474dbc"},{"name":"seal","duration":32763,"timestamp":2660938358959,"id":31,"parentId":29,"tags":{},"startTime":1753619881167,"traceId":"9380624929474dbc"},{"name":"webpack-compilation","duration":43095,"timestamp":2660938349289,"id":29,"parentId":3,"tags":{"name":"server"},"startTime":1753619881157,"traceId":"9380624929474dbc"},{"name":"emit","duration":16222,"timestamp":2660938392906,"id":44,"parentId":3,"tags":{},"startTime":1753619881201,"traceId":"9380624929474dbc"},{"name":"make","duration":381,"timestamp":2660938419613,"id":46,"parentId":45,"tags":{},"startTime":1753619881227,"traceId":"9380624929474dbc"},{"name":"chunk-graph","duration":47,"timestamp":2660938421082,"id":48,"parentId":47,"tags":{},"startTime":1753619881229,"traceId":"9380624929474dbc"},{"name":"optimize-modules","duration":9,"timestamp":2660938421166,"id":50,"parentId":47,"tags":{},"startTime":1753619881229,"traceId":"9380624929474dbc"},{"name":"optimize-chunks","duration":35,"timestamp":2660938421290,"id":51,"parentId":47,"tags":{},"startTime":1753619881229,"traceId":"9380624929474dbc"},{"name":"optimize-tree","duration":11,"timestamp":2660938421357,"id":52,"parentId":47,"tags":{},"startTime":1753619881229,"traceId":"9380624929474dbc"},{"name":"optimize-chunk-modules","duration":9,"timestamp":2660938421402,"id":53,"parentId":47,"tags":{},"startTime":1753619881229,"traceId":"9380624929474dbc"},{"name":"optimize","duration":290,"timestamp":2660938421155,"id":49,"parentId":47,"tags":{},"startTime":1753619881229,"traceId":"9380624929474dbc"},{"name":"module-hash","duration":4075,"timestamp":2660938421700,"id":54,"parentId":47,"tags":{},"startTime":1753619881229,"traceId":"9380624929474dbc"},{"name":"code-generation","duration":21,"timestamp":2660938425833,"id":55,"parentId":47,"tags":{},"startTime":1753619881233,"traceId":"9380624929474dbc"},{"name":"hash","duration":439,"timestamp":2660938426178,"id":56,"parentId":47,"tags":{},"startTime":1753619881234,"traceId":"9380624929474dbc"},{"name":"code-generation-jobs","duration":45,"timestamp":2660938426615,"id":57,"parentId":47,"tags":{},"startTime":1753619881234,"traceId":"9380624929474dbc"},{"name":"module-assets","duration":15,"timestamp":2660938426649,"id":58,"parentId":47,"tags":{},"startTime":1753619881234,"traceId":"9380624929474dbc"},{"name":"create-chunk-assets","duration":388,"timestamp":2660938426671,"id":59,"parentId":47,"tags":{},"startTime":1753619881234,"traceId":"9380624929474dbc"},{"name":"seal","duration":7799,"timestamp":2660938421012,"id":47,"parentId":45,"tags":{},"startTime":1753619881229,"traceId":"9380624929474dbc"},{"name":"webpack-compilation","duration":12201,"timestamp":2660938416764,"id":45,"parentId":3,"tags":{"name":"edge-server"},"startTime":1753619881224,"traceId":"9380624929474dbc"},{"name":"emit","duration":97141,"timestamp":2660938429386,"id":60,"parentId":3,"tags":{},"startTime":1753619881237,"traceId":"9380624929474dbc"}]
[{"name":"make","duration":609,"timestamp":2660939086916,"id":65,"parentId":64,"tags":{},"startTime":1753619881895,"traceId":"9380624929474dbc"},{"name":"chunk-graph","duration":47,"timestamp":2660939087868,"id":67,"parentId":66,"tags":{},"startTime":1753619881895,"traceId":"9380624929474dbc"},{"name":"optimize-modules","duration":9,"timestamp":2660939087947,"id":69,"parentId":66,"tags":{},"startTime":1753619881896,"traceId":"9380624929474dbc"},{"name":"optimize-chunks","duration":18,"timestamp":2660939087993,"id":70,"parentId":66,"tags":{},"startTime":1753619881896,"traceId":"9380624929474dbc"},{"name":"optimize-tree","duration":11,"timestamp":2660939088036,"id":71,"parentId":66,"tags":{},"startTime":1753619881896,"traceId":"9380624929474dbc"},{"name":"optimize-chunk-modules","duration":11,"timestamp":2660939088081,"id":72,"parentId":66,"tags":{},"startTime":1753619881896,"traceId":"9380624929474dbc"},{"name":"optimize","duration":205,"timestamp":2660939087937,"id":68,"parentId":66,"tags":{},"startTime":1753619881896,"traceId":"9380624929474dbc"},{"name":"module-hash","duration":18,"timestamp":2660939088356,"id":73,"parentId":66,"tags":{},"startTime":1753619881896,"traceId":"9380624929474dbc"},{"name":"code-generation","duration":14,"timestamp":2660939088394,"id":74,"parentId":66,"tags":{},"startTime":1753619881896,"traceId":"9380624929474dbc"},{"name":"hash","duration":112,"timestamp":2660939088469,"id":75,"parentId":66,"tags":{},"startTime":1753619881896,"traceId":"9380624929474dbc"},{"name":"code-generation-jobs","duration":41,"timestamp":2660939088580,"id":76,"parentId":66,"tags":{},"startTime":1753619881896,"traceId":"9380624929474dbc"},{"name":"module-assets","duration":18,"timestamp":2660939088609,"id":77,"parentId":66,"tags":{},"startTime":1753619881896,"traceId":"9380624929474dbc"},{"name":"create-chunk-assets","duration":29,"timestamp":2660939088637,"id":78,"parentId":66,"tags":{},"startTime":1753619881896,"traceId":"9380624929474dbc"},{"name":"NextJsBuildManifest-generateClientManifest","duration":1534,"timestamp":2660939094413,"id":80,"parentId":64,"tags":{},"startTime":1753619881902,"traceId":"9380624929474dbc"},{"name":"NextJsBuildManifest-createassets","duration":1836,"timestamp":2660939094154,"id":79,"parentId":64,"tags":{},"startTime":1753619881902,"traceId":"9380624929474dbc"},{"name":"seal","duration":8927,"timestamp":2660939087797,"id":66,"parentId":64,"tags":{},"startTime":1753619881895,"traceId":"9380624929474dbc"},{"name":"webpack-compilation","duration":11988,"timestamp":2660939085176,"id":64,"parentId":61,"tags":{"name":"client"},"startTime":1753619881893,"traceId":"9380624929474dbc"},{"name":"setup-dev-bundler","duration":4515975,"timestamp":2660934861796,"id":2,"parentId":1,"tags":{},"startTime":1753619877669,"traceId":"9380624929474dbc"},{"name":"run-instrumentation-hook","duration":102,"timestamp":2660939565038,"id":82,"parentId":1,"tags":{},"startTime":1753619882373,"traceId":"9380624929474dbc"},{"name":"emit","duration":487029,"timestamp":2660939097259,"id":81,"parentId":61,"tags":{},"startTime":1753619881905,"traceId":"9380624929474dbc"},{"name":"webpack-invalidated-client","duration":516535,"timestamp":2660939071561,"id":61,"parentId":3,"tags":{"trigger":"manual"},"startTime":1753619881879,"traceId":"9380624929474dbc"},{"name":"make","duration":867,"timestamp":2660939617724,"id":84,"parentId":83,"tags":{},"startTime":1753619882425,"traceId":"9380624929474dbc"},{"name":"chunk-graph","duration":262,"timestamp":2660939619021,"id":86,"parentId":85,"tags":{},"startTime":1753619882427,"traceId":"9380624929474dbc"},{"name":"optimize-modules","duration":19,"timestamp":2660939619555,"id":88,"parentId":85,"tags":{},"startTime":1753619882427,"traceId":"9380624929474dbc"},{"name":"optimize-chunks","duration":280,"timestamp":2660939619792,"id":89,"parentId":85,"tags":{},"startTime":1753619882427,"traceId":"9380624929474dbc"},{"name":"optimize-tree","duration":13,"timestamp":2660939620120,"id":90,"parentId":85,"tags":{},"startTime":1753619882428,"traceId":"9380624929474dbc"},{"name":"optimize-chunk-modules","duration":13,"timestamp":2660939620180,"id":91,"parentId":85,"tags":{},"startTime":1753619882428,"traceId":"9380624929474dbc"},{"name":"optimize","duration":711,"timestamp":2660939619534,"id":87,"parentId":85,"tags":{},"startTime":1753619882427,"traceId":"9380624929474dbc"},{"name":"module-hash","duration":18,"timestamp":2660939620668,"id":92,"parentId":85,"tags":{},"startTime":1753619882428,"traceId":"9380624929474dbc"},{"name":"code-generation","duration":14,"timestamp":2660939620707,"id":93,"parentId":85,"tags":{},"startTime":1753619882428,"traceId":"9380624929474dbc"},{"name":"hash","duration":196,"timestamp":2660939620794,"id":94,"parentId":85,"tags":{},"startTime":1753619882428,"traceId":"9380624929474dbc"},{"name":"code-generation-jobs","duration":42,"timestamp":2660939620989,"id":95,"parentId":85,"tags":{},"startTime":1753619882429,"traceId":"9380624929474dbc"},{"name":"module-assets","duration":16,"timestamp":2660939621020,"id":96,"parentId":85,"tags":{},"startTime":1753619882429,"traceId":"9380624929474dbc"},{"name":"create-chunk-assets","duration":27,"timestamp":2660939621045,"id":97,"parentId":85,"tags":{},"startTime":1753619882429,"traceId":"9380624929474dbc"},{"name":"seal","duration":3022,"timestamp":2660939618921,"id":85,"parentId":83,"tags":{},"startTime":1753619882427,"traceId":"9380624929474dbc"},{"name":"webpack-compilation","duration":5965,"timestamp":2660939616049,"id":83,"parentId":62,"tags":{"name":"server"},"startTime":1753619882424,"traceId":"9380624929474dbc"},{"name":"start-dev-server","duration":5863904,"timestamp":2660933766449,"id":1,"tags":{"cpus":"4","platform":"win32","memory.freeMem":"809058304","memory.totalMem":"8320221184","memory.heapSizeLimit":"4210032640","isTurbopack":false,"memory.rss":"183853056","memory.heapTotal":"122601472","memory.heapUsed":"98761544"},"startTime":1753619876574,"traceId":"9380624929474dbc"},{"name":"emit","duration":19270,"timestamp":2660939622080,"id":98,"parentId":62,"tags":{},"startTime":1753619882430,"traceId":"9380624929474dbc"},{"name":"webpack-invalidated-server","duration":571655,"timestamp":2660939071903,"id":62,"parentId":3,"tags":{"trigger":"manual"},"startTime":1753619881880,"traceId":"9380624929474dbc"},{"name":"make","duration":594,"timestamp":2660939651418,"id":100,"parentId":99,"tags":{},"startTime":1753619882459,"traceId":"9380624929474dbc"},{"name":"chunk-graph","duration":42,"timestamp":2660939652567,"id":102,"parentId":101,"tags":{},"startTime":1753619882460,"traceId":"9380624929474dbc"},{"name":"optimize-modules","duration":10,"timestamp":2660939652646,"id":104,"parentId":101,"tags":{},"startTime":1753619882460,"traceId":"9380624929474dbc"},{"name":"optimize-chunks","duration":32,"timestamp":2660939652684,"id":105,"parentId":101,"tags":{},"startTime":1753619882460,"traceId":"9380624929474dbc"},{"name":"optimize-tree","duration":11,"timestamp":2660939652741,"id":106,"parentId":101,"tags":{},"startTime":1753619882460,"traceId":"9380624929474dbc"},{"name":"optimize-chunk-modules","duration":12,"timestamp":2660939652784,"id":107,"parentId":101,"tags":{},"startTime":1753619882460,"traceId":"9380624929474dbc"},{"name":"optimize","duration":198,"timestamp":2660939652630,"id":103,"parentId":101,"tags":{},"startTime":1753619882460,"traceId":"9380624929474dbc"},{"name":"module-hash","duration":13,"timestamp":2660939652990,"id":108,"parentId":101,"tags":{},"startTime":1753619882461,"traceId":"9380624929474dbc"},{"name":"code-generation","duration":12,"timestamp":2660939653018,"id":109,"parentId":101,"tags":{},"startTime":1753619882461,"traceId":"9380624929474dbc"},{"name":"hash","duration":98,"timestamp":2660939653080,"id":110,"parentId":101,"tags":{},"startTime":1753619882461,"traceId":"9380624929474dbc"},{"name":"code-generation-jobs","duration":31,"timestamp":2660939653178,"id":111,"parentId":101,"tags":{},"startTime":1753619882461,"traceId":"9380624929474dbc"},{"name":"module-assets","duration":13,"timestamp":2660939653199,"id":112,"parentId":101,"tags":{},"startTime":1753619882461,"traceId":"9380624929474dbc"},{"name":"create-chunk-assets","duration":23,"timestamp":2660939653220,"id":113,"parentId":101,"tags":{},"startTime":1753619882461,"traceId":"9380624929474dbc"},{"name":"seal","duration":1969,"timestamp":2660939652510,"id":101,"parentId":99,"tags":{},"startTime":1753619882460,"traceId":"9380624929474dbc"},{"name":"webpack-compilation","duration":4645,"timestamp":2660939649907,"id":99,"parentId":63,"tags":{"name":"edge-server"},"startTime":1753619882458,"traceId":"9380624929474dbc"},{"name":"emit","duration":21372,"timestamp":2660939654618,"id":114,"parentId":63,"tags":{},"startTime":1753619882462,"traceId":"9380624929474dbc"},{"name":"webpack-invalidated-edge-server","duration":605393,"timestamp":2660939071978,"id":63,"parentId":3,"tags":{"trigger":"manual"},"startTime":1753619881880,"traceId":"9380624929474dbc"}]
[{"name":"add-entry","duration":1114031,"timestamp":2660952419973,"id":120,"parentId":119,"tags":{"request":"next-app-loader?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5Canimted-Portfolio%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5Canimted-Portfolio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1753619895228,"traceId":"9380624929474dbc"},{"name":"make","duration":1698229,"timestamp":2660952400285,"id":119,"parentId":118,"tags":{},"startTime":1753619895208,"traceId":"9380624929474dbc"},{"name":"chunk-graph","duration":17407,"timestamp":2660954146242,"id":126,"parentId":125,"tags":{},"startTime":1753619896954,"traceId":"9380624929474dbc"},{"name":"optimize-modules","duration":15,"timestamp":2660954163729,"id":128,"parentId":125,"tags":{},"startTime":1753619896971,"traceId":"9380624929474dbc"},{"name":"optimize-chunks","duration":20169,"timestamp":2660954163781,"id":129,"parentId":125,"tags":{},"startTime":1753619896971,"traceId":"9380624929474dbc"},{"name":"optimize-tree","duration":26,"timestamp":2660954184037,"id":130,"parentId":125,"tags":{},"startTime":1753619896992,"traceId":"9380624929474dbc"},{"name":"optimize-chunk-modules","duration":172,"timestamp":2660954184099,"id":131,"parentId":125,"tags":{},"startTime":1753619896992,"traceId":"9380624929474dbc"},{"name":"optimize","duration":24466,"timestamp":2660954163706,"id":127,"parentId":125,"tags":{},"startTime":1753619896971,"traceId":"9380624929474dbc"},{"name":"module-hash","duration":27082,"timestamp":2660954208565,"id":132,"parentId":125,"tags":{},"startTime":1753619897016,"traceId":"9380624929474dbc"},{"name":"code-generation","duration":9516,"timestamp":2660954235708,"id":133,"parentId":125,"tags":{},"startTime":1753619897043,"traceId":"9380624929474dbc"},{"name":"hash","duration":15321,"timestamp":2660954254719,"id":134,"parentId":125,"tags":{},"startTime":1753619897062,"traceId":"9380624929474dbc"},{"name":"code-generation-jobs","duration":491,"timestamp":2660954270037,"id":135,"parentId":125,"tags":{},"startTime":1753619897078,"traceId":"9380624929474dbc"},{"name":"module-assets","duration":600,"timestamp":2660954270478,"id":136,"parentId":125,"tags":{},"startTime":1753619897078,"traceId":"9380624929474dbc"},{"name":"create-chunk-assets","duration":67415,"timestamp":2660954271096,"id":137,"parentId":125,"tags":{},"startTime":1753619897079,"traceId":"9380624929474dbc"},{"name":"seal","duration":216696,"timestamp":2660954135674,"id":125,"parentId":118,"tags":{},"startTime":1753619896943,"traceId":"9380624929474dbc"},{"name":"webpack-compilation","duration":1980296,"timestamp":2660952381194,"id":118,"parentId":116,"tags":{"name":"server"},"startTime":1753619895189,"traceId":"9380624929474dbc"},{"name":"emit","duration":129743,"timestamp":2660954361782,"id":138,"parentId":116,"tags":{},"startTime":1753619897169,"traceId":"9380624929474dbc"},{"name":"webpack-invalidated-server","duration":2115723,"timestamp":2660952376819,"id":116,"parentId":3,"tags":{"trigger":"manual"},"startTime":1753619895184,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":509814,"timestamp":2660954505518,"id":141,"parentId":140,"tags":{"request":"D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1753619897313,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":1938713,"timestamp":2660954509108,"id":145,"parentId":140,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1753619897317,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":2206032,"timestamp":2660954508518,"id":142,"parentId":140,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1753619897316,"traceId":"9380624929474dbc"},{"name":"read-resource","duration":333131,"timestamp":2660956447358,"id":148,"parentId":147,"tags":{},"startTime":1753619899255,"traceId":"9380624929474dbc"},{"name":"postcss-process","duration":1732938,"timestamp":2660959814461,"id":150,"parentId":149,"tags":{},"startTime":1753619902622,"traceId":"9380624929474dbc"},{"name":"postcss-loader","duration":4775825,"timestamp":2660956781035,"id":149,"parentId":147,"tags":{},"startTime":1753619899589,"traceId":"9380624929474dbc"},{"name":"css-loader","duration":290299,"timestamp":2660961557638,"id":151,"parentId":147,"tags":{"astUsed":"true"},"startTime":1753619904365,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":7716879,"timestamp":2660954508681,"id":143,"parentId":140,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1753619897316,"traceId":"9380624929474dbc"},{"name":"build-module-css","duration":5878423,"timestamp":2660956360013,"id":147,"parentId":146,"tags":{"name":"D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\app\\globals.css.webpack[javascript/auto]!=!D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[14].oneOf[12].use[2]!D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[14].oneOf[12].use[3]!D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\app\\globals.css","layer":null},"startTime":1753619899168,"traceId":"9380624929474dbc"},{"name":"build-module-css","duration":6599747,"timestamp":2660955701044,"id":146,"parentId":139,"tags":{"name":"D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\app\\globals.css","layer":"app-pages-browser"},"startTime":1753619898509,"traceId":"9380624929474dbc"},{"name":"build-module","duration":442,"timestamp":2660962302634,"id":152,"parentId":146,"tags":{},"startTime":1753619905110,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":7794498,"timestamp":2660954509046,"id":144,"parentId":140,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1753619897317,"traceId":"9380624929474dbc"},{"name":"make","duration":7807897,"timestamp":2660954496326,"id":140,"parentId":139,"tags":{},"startTime":1753619897304,"traceId":"9380624929474dbc"},{"name":"chunk-graph","duration":35229,"timestamp":2660962419224,"id":154,"parentId":153,"tags":{},"startTime":1753619905227,"traceId":"9380624929474dbc"},{"name":"optimize-modules","duration":290,"timestamp":2660962454789,"id":156,"parentId":153,"tags":{},"startTime":1753619905262,"traceId":"9380624929474dbc"},{"name":"optimize-chunks","duration":1278,"timestamp":2660962455599,"id":157,"parentId":153,"tags":{},"startTime":1753619905263,"traceId":"9380624929474dbc"},{"name":"optimize-tree","duration":305,"timestamp":2660962457058,"id":158,"parentId":153,"tags":{},"startTime":1753619905265,"traceId":"9380624929474dbc"},{"name":"optimize-chunk-modules","duration":334,"timestamp":2660962457543,"id":159,"parentId":153,"tags":{},"startTime":1753619905265,"traceId":"9380624929474dbc"},{"name":"optimize","duration":15137,"timestamp":2660962454663,"id":155,"parentId":153,"tags":{},"startTime":1753619905262,"traceId":"9380624929474dbc"},{"name":"module-hash","duration":46075,"timestamp":2660962511160,"id":160,"parentId":153,"tags":{},"startTime":1753619905319,"traceId":"9380624929474dbc"},{"name":"code-generation","duration":17417,"timestamp":2660962557446,"id":161,"parentId":153,"tags":{},"startTime":1753619905365,"traceId":"9380624929474dbc"},{"name":"hash","duration":80535,"timestamp":2660962595474,"id":162,"parentId":153,"tags":{},"startTime":1753619905403,"traceId":"9380624929474dbc"},{"name":"code-generation-jobs","duration":1122,"timestamp":2660962676003,"id":163,"parentId":153,"tags":{},"startTime":1753619905484,"traceId":"9380624929474dbc"},{"name":"module-assets","duration":943,"timestamp":2660962677069,"id":164,"parentId":153,"tags":{},"startTime":1753619905485,"traceId":"9380624929474dbc"},{"name":"create-chunk-assets","duration":42341,"timestamp":2660962678054,"id":165,"parentId":153,"tags":{},"startTime":1753619905486,"traceId":"9380624929474dbc"},{"name":"NextJsBuildManifest-generateClientManifest","duration":2259,"timestamp":2660962731717,"id":167,"parentId":139,"tags":{},"startTime":1753619905539,"traceId":"9380624929474dbc"},{"name":"NextJsBuildManifest-createassets","duration":4638,"timestamp":2660962729375,"id":166,"parentId":139,"tags":{},"startTime":1753619905537,"traceId":"9380624929474dbc"},{"name":"seal","duration":355695,"timestamp":2660962395134,"id":153,"parentId":139,"tags":{},"startTime":1753619905203,"traceId":"9380624929474dbc"},{"name":"webpack-compilation","duration":8255691,"timestamp":2660954495848,"id":139,"parentId":124,"tags":{"name":"client"},"startTime":1753619897303,"traceId":"9380624929474dbc"},{"name":"emit","duration":197849,"timestamp":2660962752710,"id":168,"parentId":124,"tags":{},"startTime":1753619905560,"traceId":"9380624929474dbc"},{"name":"compile-path","duration":10586759,"timestamp":2660952376935,"id":117,"tags":{"trigger":"/","isTurbopack":false},"startTime":1753619895185,"traceId":"9380624929474dbc"},{"name":"webpack-invalidated-client","duration":9409109,"timestamp":2660953558654,"id":124,"parentId":3,"tags":{"trigger":"manual"},"startTime":1753619896366,"traceId":"9380624929474dbc"}]
[{"name":"add-entry","duration":1278681,"timestamp":2660963167383,"id":172,"parentId":171,"tags":{"request":"D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1753619905975,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":1356566,"timestamp":2660963167946,"id":176,"parentId":171,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1753619905976,"traceId":"9380624929474dbc"},{"name":"read-resource","duration":27835,"timestamp":2660964497067,"id":179,"parentId":178,"tags":{},"startTime":1753619907305,"traceId":"9380624929474dbc"},{"name":"postcss-process","duration":250848,"timestamp":2660964525079,"id":181,"parentId":180,"tags":{},"startTime":1753619907333,"traceId":"9380624929474dbc"},{"name":"postcss-loader","duration":252401,"timestamp":2660964524984,"id":180,"parentId":178,"tags":{},"startTime":1753619907333,"traceId":"9380624929474dbc"},{"name":"css-loader","duration":71147,"timestamp":2660964777614,"id":182,"parentId":178,"tags":{"astUsed":"true"},"startTime":1753619907585,"traceId":"9380624929474dbc"},{"name":"build-module-css","duration":379910,"timestamp":2660964496425,"id":178,"parentId":177,"tags":{"name":"D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\app\\globals.css.webpack[javascript/auto]!=!D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[14].oneOf[12].use[2]!D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[14].oneOf[12].use[3]!D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\app\\globals.css","layer":null},"startTime":1753619907304,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":1750030,"timestamp":2660963167811,"id":173,"parentId":171,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1753619905975,"traceId":"9380624929474dbc"},{"name":"build-module-css","duration":850273,"timestamp":2660964087744,"id":177,"parentId":170,"tags":{"name":"D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\app\\globals.css","layer":"app-pages-browser"},"startTime":1753619906895,"traceId":"9380624929474dbc"},{"name":"build-module","duration":209,"timestamp":2660964953164,"id":183,"parentId":177,"tags":{},"startTime":1753619907761,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":1795326,"timestamp":2660963167914,"id":175,"parentId":171,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1753619905976,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":1836642,"timestamp":2660963167878,"id":174,"parentId":171,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1753619905976,"traceId":"9380624929474dbc"},{"name":"make","duration":1841847,"timestamp":2660963162749,"id":171,"parentId":170,"tags":{},"startTime":1753619905970,"traceId":"9380624929474dbc"},{"name":"chunk-graph","duration":12021,"timestamp":2660965043238,"id":185,"parentId":184,"tags":{},"startTime":1753619907851,"traceId":"9380624929474dbc"},{"name":"optimize-modules","duration":56,"timestamp":2660965055535,"id":187,"parentId":184,"tags":{},"startTime":1753619907863,"traceId":"9380624929474dbc"},{"name":"optimize-chunks","duration":1026,"timestamp":2660965055987,"id":188,"parentId":184,"tags":{},"startTime":1753619907864,"traceId":"9380624929474dbc"},{"name":"optimize-tree","duration":27,"timestamp":2660965057099,"id":189,"parentId":184,"tags":{},"startTime":1753619907865,"traceId":"9380624929474dbc"},{"name":"optimize-chunk-modules","duration":71,"timestamp":2660965059739,"id":190,"parentId":184,"tags":{},"startTime":1753619907867,"traceId":"9380624929474dbc"},{"name":"optimize","duration":11015,"timestamp":2660965055402,"id":186,"parentId":184,"tags":{},"startTime":1753619907863,"traceId":"9380624929474dbc"},{"name":"module-hash","duration":1981,"timestamp":2660965075494,"id":191,"parentId":184,"tags":{},"startTime":1753619907883,"traceId":"9380624929474dbc"},{"name":"code-generation","duration":21627,"timestamp":2660965077539,"id":192,"parentId":184,"tags":{},"startTime":1753619907885,"traceId":"9380624929474dbc"},{"name":"hash","duration":12775,"timestamp":2660965104319,"id":193,"parentId":184,"tags":{},"startTime":1753619907912,"traceId":"9380624929474dbc"},{"name":"code-generation-jobs","duration":603,"timestamp":2660965117090,"id":194,"parentId":184,"tags":{},"startTime":1753619907925,"traceId":"9380624929474dbc"},{"name":"module-assets","duration":679,"timestamp":2660965117646,"id":195,"parentId":184,"tags":{},"startTime":1753619907925,"traceId":"9380624929474dbc"},{"name":"create-chunk-assets","duration":8492,"timestamp":2660965118360,"id":196,"parentId":184,"tags":{},"startTime":1753619907926,"traceId":"9380624929474dbc"},{"name":"NextJsBuildManifest-generateClientManifest","duration":196,"timestamp":2660965132650,"id":198,"parentId":170,"tags":{},"startTime":1753619907940,"traceId":"9380624929474dbc"},{"name":"NextJsBuildManifest-createassets","duration":381,"timestamp":2660965132491,"id":197,"parentId":170,"tags":{},"startTime":1753619907940,"traceId":"9380624929474dbc"},{"name":"seal","duration":113250,"timestamp":2660965025099,"id":184,"parentId":170,"tags":{},"startTime":1753619907833,"traceId":"9380624929474dbc"},{"name":"webpack-compilation","duration":1995035,"timestamp":2660963143438,"id":170,"parentId":169,"tags":{"name":"client"},"startTime":1753619905951,"traceId":"9380624929474dbc"},{"name":"handle-request","duration":12991587,"timestamp":2660952156264,"id":115,"tags":{"url":"/","isTurbopack":false},"startTime":1753619894964,"traceId":"9380624929474dbc"},{"name":"memory-usage","duration":4,"timestamp":2660965148378,"id":200,"parentId":115,"tags":{"url":"/","memory.rss":"477794304","memory.heapUsed":"245813496","memory.heapTotal":"274894848"},"startTime":1753619907956,"traceId":"9380624929474dbc"},{"name":"emit","duration":194116,"timestamp":2660965138556,"id":199,"parentId":169,"tags":{},"startTime":1753619907946,"traceId":"9380624929474dbc"},{"name":"webpack-invalidated-client","duration":2222188,"timestamp":2660963114684,"id":169,"parentId":3,"tags":{"trigger":"manual"},"startTime":1753619905922,"traceId":"9380624929474dbc"}]
[{"name":"client-success","duration":30,"timestamp":2660967552627,"id":201,"parentId":3,"tags":{},"startTime":1753619910360,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":682925,"timestamp":2660975199368,"id":208,"parentId":206,"tags":{"request":"next-app-loader?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5Canimted-Portfolio%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5Canimted-Portfolio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1753619918007,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":901116,"timestamp":2660975199008,"id":207,"parentId":206,"tags":{"request":"next-app-loader?name=app%2Fcontact%2Fpage&page=%2Fcontact%2Fpage&appPaths=%2Fcontact%2Fpage&pagePath=private-next-app-dir%2Fcontact%2Fpage.tsx&appDir=D%3A%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5Canimted-Portfolio%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5Canimted-Portfolio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!"},"startTime":1753619918007,"traceId":"9380624929474dbc"},{"name":"make","duration":1277171,"timestamp":2660975190440,"id":206,"parentId":205,"tags":{},"startTime":1753619917998,"traceId":"9380624929474dbc"},{"name":"chunk-graph","duration":43314,"timestamp":2660976576427,"id":217,"parentId":216,"tags":{},"startTime":1753619919384,"traceId":"9380624929474dbc"},{"name":"optimize-modules","duration":72,"timestamp":2660976620825,"id":219,"parentId":216,"tags":{},"startTime":1753619919428,"traceId":"9380624929474dbc"},{"name":"optimize-chunks","duration":50457,"timestamp":2660976621451,"id":220,"parentId":216,"tags":{},"startTime":1753619919429,"traceId":"9380624929474dbc"},{"name":"optimize-tree","duration":71,"timestamp":2660976672074,"id":221,"parentId":216,"tags":{},"startTime":1753619919480,"traceId":"9380624929474dbc"},{"name":"optimize-chunk-modules","duration":40,"timestamp":2660976672277,"id":222,"parentId":216,"tags":{},"startTime":1753619919480,"traceId":"9380624929474dbc"},{"name":"optimize","duration":79181,"timestamp":2660976620733,"id":218,"parentId":216,"tags":{},"startTime":1753619919428,"traceId":"9380624929474dbc"},{"name":"module-hash","duration":10452,"timestamp":2660976720216,"id":223,"parentId":216,"tags":{},"startTime":1753619919528,"traceId":"9380624929474dbc"},{"name":"code-generation","duration":7431,"timestamp":2660976730739,"id":224,"parentId":216,"tags":{},"startTime":1753619919538,"traceId":"9380624929474dbc"},{"name":"hash","duration":10905,"timestamp":2660976761826,"id":225,"parentId":216,"tags":{},"startTime":1753619919569,"traceId":"9380624929474dbc"},{"name":"code-generation-jobs","duration":1205,"timestamp":2660976772724,"id":226,"parentId":216,"tags":{},"startTime":1753619919580,"traceId":"9380624929474dbc"},{"name":"module-assets","duration":2140,"timestamp":2660976773853,"id":227,"parentId":216,"tags":{},"startTime":1753619919581,"traceId":"9380624929474dbc"},{"name":"create-chunk-assets","duration":12891,"timestamp":2660976776069,"id":228,"parentId":216,"tags":{},"startTime":1753619919584,"traceId":"9380624929474dbc"},{"name":"seal","duration":274747,"timestamp":2660976527470,"id":216,"parentId":205,"tags":{},"startTime":1753619919335,"traceId":"9380624929474dbc"},{"name":"webpack-compilation","duration":1644466,"timestamp":2660975186795,"id":205,"parentId":203,"tags":{"name":"server"},"startTime":1753619917994,"traceId":"9380624929474dbc"},{"name":"emit","duration":35912,"timestamp":2660976831608,"id":229,"parentId":203,"tags":{},"startTime":1753619919639,"traceId":"9380624929474dbc"},{"name":"webpack-invalidated-server","duration":1701677,"timestamp":2660975168652,"id":203,"parentId":3,"tags":{"trigger":"manual"},"startTime":1753619917976,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":135949,"timestamp":2660976918402,"id":232,"parentId":231,"tags":{"request":"D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1753619919726,"traceId":"9380624929474dbc"},{"name":"read-resource","duration":932343,"timestamp":2660977081265,"id":240,"parentId":239,"tags":{},"startTime":1753619919889,"traceId":"9380624929474dbc"},{"name":"postcss-process","duration":405814,"timestamp":2660978014740,"id":242,"parentId":241,"tags":{},"startTime":1753619920822,"traceId":"9380624929474dbc"},{"name":"postcss-loader","duration":416443,"timestamp":2660978014348,"id":241,"parentId":239,"tags":{},"startTime":1753619920822,"traceId":"9380624929474dbc"},{"name":"css-loader","duration":167068,"timestamp":2660978432467,"id":243,"parentId":239,"tags":{"astUsed":"true"},"startTime":1753619921240,"traceId":"9380624929474dbc"},{"name":"build-module-css","duration":1583413,"timestamp":2660977076954,"id":239,"parentId":238,"tags":{"name":"D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\app\\globals.css.webpack[javascript/auto]!=!D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[14].oneOf[12].use[2]!D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[14].oneOf[12].use[3]!D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\app\\globals.css","layer":null},"startTime":1753619919885,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":1782761,"timestamp":2660976919053,"id":236,"parentId":231,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1753619919727,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":1810314,"timestamp":2660976918913,"id":233,"parentId":231,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1753619919727,"traceId":"9380624929474dbc"},{"name":"build-module-css","duration":1898711,"timestamp":2660976954935,"id":238,"parentId":230,"tags":{"name":"D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\app\\globals.css","layer":"app-pages-browser"},"startTime":1753619919763,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":1936216,"timestamp":2660976918987,"id":234,"parentId":231,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1753619919727,"traceId":"9380624929474dbc"},{"name":"build-module","duration":213,"timestamp":2660978879138,"id":244,"parentId":238,"tags":{},"startTime":1753619921687,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":1960490,"timestamp":2660976919015,"id":235,"parentId":231,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1753619919727,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":2327233,"timestamp":2660976919073,"id":237,"parentId":231,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Capp%5C%5Ccontact%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1753619919727,"traceId":"9380624929474dbc"},{"name":"make","duration":2361121,"timestamp":2660976885453,"id":231,"parentId":230,"tags":{},"startTime":1753619919693,"traceId":"9380624929474dbc"},{"name":"chunk-graph","duration":35630,"timestamp":2660979397190,"id":246,"parentId":245,"tags":{},"startTime":1753619922205,"traceId":"9380624929474dbc"},{"name":"optimize-modules","duration":72,"timestamp":2660979433322,"id":248,"parentId":245,"tags":{},"startTime":1753619922241,"traceId":"9380624929474dbc"},{"name":"optimize-chunks","duration":750,"timestamp":2660979433766,"id":249,"parentId":245,"tags":{},"startTime":1753619922241,"traceId":"9380624929474dbc"},{"name":"optimize-tree","duration":2380,"timestamp":2660979434662,"id":250,"parentId":245,"tags":{},"startTime":1753619922242,"traceId":"9380624929474dbc"},{"name":"optimize-chunk-modules","duration":49,"timestamp":2660979437269,"id":251,"parentId":245,"tags":{},"startTime":1753619922245,"traceId":"9380624929474dbc"},{"name":"optimize","duration":22332,"timestamp":2660979433233,"id":247,"parentId":245,"tags":{},"startTime":1753619922241,"traceId":"9380624929474dbc"},{"name":"module-hash","duration":10303,"timestamp":2660979536608,"id":252,"parentId":245,"tags":{},"startTime":1753619922344,"traceId":"9380624929474dbc"},{"name":"code-generation","duration":30200,"timestamp":2660979547050,"id":253,"parentId":245,"tags":{},"startTime":1753619922355,"traceId":"9380624929474dbc"},{"name":"hash","duration":54658,"timestamp":2660979598397,"id":254,"parentId":245,"tags":{},"startTime":1753619922406,"traceId":"9380624929474dbc"},{"name":"code-generation-jobs","duration":555,"timestamp":2660979653051,"id":255,"parentId":245,"tags":{},"startTime":1753619922461,"traceId":"9380624929474dbc"},{"name":"module-assets","duration":861,"timestamp":2660979653535,"id":256,"parentId":245,"tags":{},"startTime":1753619922461,"traceId":"9380624929474dbc"},{"name":"create-chunk-assets","duration":16006,"timestamp":2660979654439,"id":257,"parentId":245,"tags":{},"startTime":1753619922462,"traceId":"9380624929474dbc"},{"name":"NextJsBuildManifest-generateClientManifest","duration":2107,"timestamp":2660979682392,"id":259,"parentId":230,"tags":{},"startTime":1753619922490,"traceId":"9380624929474dbc"},{"name":"NextJsBuildManifest-createassets","duration":3557,"timestamp":2660979680976,"id":258,"parentId":230,"tags":{},"startTime":1753619922489,"traceId":"9380624929474dbc"},{"name":"seal","duration":374330,"timestamp":2660979329051,"id":245,"parentId":230,"tags":{},"startTime":1753619922137,"traceId":"9380624929474dbc"},{"name":"webpack-compilation","duration":2820267,"timestamp":2660976883550,"id":230,"parentId":215,"tags":{"name":"client"},"startTime":1753619919691,"traceId":"9380624929474dbc"},{"name":"emit","duration":57683,"timestamp":2660979704137,"id":260,"parentId":215,"tags":{},"startTime":1753619922512,"traceId":"9380624929474dbc"},{"name":"compile-path","duration":4599743,"timestamp":2660975169230,"id":204,"tags":{"trigger":"/contact","isTurbopack":false},"startTime":1753619917977,"traceId":"9380624929474dbc"},{"name":"webpack-invalidated-client","duration":3646578,"timestamp":2660976130528,"id":215,"parentId":3,"tags":{"trigger":"manual"},"startTime":1753619918938,"traceId":"9380624929474dbc"}]
[{"name":"client-success","duration":42,"timestamp":2660979814516,"id":261,"parentId":3,"tags":{},"startTime":1753619922622,"traceId":"9380624929474dbc"},{"name":"handle-request","duration":4917427,"timestamp":2660975145171,"id":202,"tags":{"url":"/contact?_rsc=1wtp7","isTurbopack":false},"startTime":1753619917953,"traceId":"9380624929474dbc"},{"name":"memory-usage","duration":6,"timestamp":2660980062860,"id":262,"parentId":202,"tags":{"url":"/contact?_rsc=1wtp7","memory.rss":"430948352","memory.heapUsed":"217206784","memory.heapTotal":"260841472"},"startTime":1753619922870,"traceId":"9380624929474dbc"},{"name":"client-hmr-latency","duration":3935000,"timestamp":2660976135095,"id":264,"parentId":3,"tags":{"updatedModules":[],"page":"/","isPageHidden":false},"startTime":1753619922885,"traceId":"9380624929474dbc"},{"name":"client-hmr-latency","duration":3957000,"timestamp":2660976134934,"id":265,"parentId":3,"tags":{"updatedModules":[],"page":"/","isPageHidden":false},"startTime":1753619922954,"traceId":"9380624929474dbc"},{"name":"handle-request","duration":84129,"timestamp":2660980067624,"id":263,"tags":{"url":"/contact?_rsc=1rc55","isTurbopack":false},"startTime":1753619922875,"traceId":"9380624929474dbc"},{"name":"memory-usage","duration":6,"timestamp":2660980151869,"id":266,"parentId":263,"tags":{"url":"/contact?_rsc=1rc55","memory.rss":"430964736","memory.heapUsed":"218645992","memory.heapTotal":"260841472"},"startTime":1753619922960,"traceId":"9380624929474dbc"},{"name":"handle-request","duration":325715,"timestamp":2663694762961,"id":267,"tags":{"url":"/contact?_rsc=1wtp7","isTurbopack":false},"startTime":1753622637572,"traceId":"9380624929474dbc"},{"name":"memory-usage","duration":7,"timestamp":2663695090098,"id":268,"parentId":267,"tags":{"url":"/contact?_rsc=1wtp7","memory.rss":"31510528","memory.heapUsed":"185268192","memory.heapTotal":"209100800"},"startTime":1753622637899,"traceId":"9380624929474dbc"},{"name":"handle-request","duration":2730663,"timestamp":2823844701188,"id":269,"tags":{"url":"/contact","isTurbopack":false},"startTime":1753782773510,"traceId":"9380624929474dbc"},{"name":"memory-usage","duration":16,"timestamp":2823847436928,"id":270,"parentId":269,"tags":{"url":"/contact","memory.rss":"71233536","memory.heapUsed":"192962144","memory.heapTotal":"209960960"},"startTime":1753782776245,"traceId":"9380624929474dbc"},{"name":"client-success","duration":73,"timestamp":2823848339422,"id":271,"parentId":3,"tags":{},"startTime":1753782777148,"traceId":"9380624929474dbc"},{"name":"handle-request","duration":116489,"timestamp":2823868052791,"id":272,"tags":{"url":"/?_rsc=zewlb","isTurbopack":false},"startTime":1753782796861,"traceId":"9380624929474dbc"},{"name":"memory-usage","duration":4,"timestamp":2823868169405,"id":273,"parentId":272,"tags":{"url":"/?_rsc=zewlb","memory.rss":"79781888","memory.heapUsed":"194098120","memory.heapTotal":"211836928"},"startTime":1753782796978,"traceId":"9380624929474dbc"},{"name":"handle-request","duration":50492,"timestamp":2823868178717,"id":274,"tags":{"url":"/?_rsc=1h9g0","isTurbopack":false},"startTime":1753782796987,"traceId":"9380624929474dbc"},{"name":"memory-usage","duration":5,"timestamp":2823868229710,"id":275,"parentId":274,"tags":{"url":"/?_rsc=1h9g0","memory.rss":"80375808","memory.heapUsed":"194180856","memory.heapTotal":"210788352"},"startTime":1753782797038,"traceId":"9380624929474dbc"},{"name":"handle-request","duration":42682,"timestamp":2823880530483,"id":276,"tags":{"url":"/contact?_rsc=5cklm","isTurbopack":false},"startTime":1753782809339,"traceId":"9380624929474dbc"},{"name":"memory-usage","duration":7,"timestamp":2823880573343,"id":277,"parentId":276,"tags":{"url":"/contact?_rsc=5cklm","memory.rss":"83337216","memory.heapUsed":"194889648","memory.heapTotal":"210788352"},"startTime":1753782809382,"traceId":"9380624929474dbc"},{"name":"handle-request","duration":588186,"timestamp":2828254947290,"id":278,"tags":{"url":"/contact","isTurbopack":false},"startTime":1753787183754,"traceId":"9380624929474dbc"},{"name":"memory-usage","duration":11,"timestamp":2828255536753,"id":279,"parentId":278,"tags":{"url":"/contact","memory.rss":"46448640","memory.heapUsed":"195190712","memory.heapTotal":"212099072"},"startTime":1753787184344,"traceId":"9380624929474dbc"},{"name":"client-success","duration":37,"timestamp":2828257181585,"id":280,"parentId":3,"tags":{},"startTime":1753787185989,"traceId":"9380624929474dbc"},{"name":"handle-request","duration":39480,"timestamp":2828259672263,"id":281,"tags":{"url":"/?_rsc=zewlb","isTurbopack":false},"startTime":1753787188479,"traceId":"9380624929474dbc"},{"name":"memory-usage","duration":6,"timestamp":2828259711895,"id":282,"parentId":281,"tags":{"url":"/?_rsc=zewlb","memory.rss":"183480320","memory.heapUsed":"181518248","memory.heapTotal":"208228352"},"startTime":1753787188519,"traceId":"9380624929474dbc"},{"name":"handle-request","duration":28301,"timestamp":2828259724748,"id":283,"tags":{"url":"/?_rsc=1h9g0","isTurbopack":false},"startTime":1753787188532,"traceId":"9380624929474dbc"},{"name":"memory-usage","duration":5,"timestamp":2828259753406,"id":284,"parentId":283,"tags":{"url":"/?_rsc=1h9g0","memory.rss":"183623680","memory.heapUsed":"181746688","memory.heapTotal":"208228352"},"startTime":1753787188560,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":201574,"timestamp":2828735458667,"id":288,"parentId":287,"tags":{"request":"D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1753787664267,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":290660,"timestamp":2828735462434,"id":292,"parentId":287,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1753787664270,"traceId":"9380624929474dbc"},{"name":"read-resource","duration":34917,"timestamp":2828735718782,"id":296,"parentId":295,"tags":{},"startTime":1753787664527,"traceId":"9380624929474dbc"},{"name":"postcss-process","duration":375668,"timestamp":2828735755811,"id":298,"parentId":297,"tags":{},"startTime":1753787664564,"traceId":"9380624929474dbc"},{"name":"postcss-loader","duration":386593,"timestamp":2828735754491,"id":297,"parentId":295,"tags":{},"startTime":1753787664562,"traceId":"9380624929474dbc"},{"name":"css-loader","duration":194056,"timestamp":2828736141746,"id":299,"parentId":295,"tags":{"astUsed":"true"},"startTime":1753787664950,"traceId":"9380624929474dbc"},{"name":"build-module-css","duration":686835,"timestamp":2828735716778,"id":295,"parentId":294,"tags":{"name":"D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\app\\globals.css.webpack[javascript/auto]!=!D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[14].oneOf[12].use[2]!D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[14].oneOf[12].use[3]!D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\app\\globals.css","layer":null},"startTime":1753787664525,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":945467,"timestamp":2828735462510,"id":293,"parentId":287,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Capp%5C%5Ccontact%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1753787664270,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":976992,"timestamp":2828735462256,"id":289,"parentId":287,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1753787664270,"traceId":"9380624929474dbc"},{"name":"build-module-css","duration":918813,"timestamp":2828735579197,"id":294,"parentId":286,"tags":{"name":"D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\app\\globals.css","layer":"app-pages-browser"},"startTime":1753787664387,"traceId":"9380624929474dbc"},{"name":"build-module","duration":534,"timestamp":2828736512082,"id":300,"parentId":294,"tags":{},"startTime":1753787665320,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":1051948,"timestamp":2828735462405,"id":291,"parentId":287,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1753787664270,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":1083042,"timestamp":2828735462320,"id":290,"parentId":287,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1753787664270,"traceId":"9380624929474dbc"},{"name":"make","duration":1114305,"timestamp":2828735431696,"id":287,"parentId":286,"tags":{},"startTime":1753787664240,"traceId":"9380624929474dbc"},{"name":"chunk-graph","duration":38207,"timestamp":2828737169638,"id":302,"parentId":301,"tags":{},"startTime":1753787665977,"traceId":"9380624929474dbc"},{"name":"optimize-modules","duration":73,"timestamp":2828737208407,"id":304,"parentId":301,"tags":{},"startTime":1753787666016,"traceId":"9380624929474dbc"},{"name":"optimize-chunks","duration":1569,"timestamp":2828737209039,"id":305,"parentId":301,"tags":{},"startTime":1753787666017,"traceId":"9380624929474dbc"},{"name":"optimize-tree","duration":1437,"timestamp":2828737211088,"id":306,"parentId":301,"tags":{},"startTime":1753787666019,"traceId":"9380624929474dbc"},{"name":"optimize-chunk-modules","duration":2784,"timestamp":2828737213038,"id":307,"parentId":301,"tags":{},"startTime":1753787666021,"traceId":"9380624929474dbc"},{"name":"optimize","duration":31725,"timestamp":2828737208268,"id":303,"parentId":301,"tags":{},"startTime":1753787666016,"traceId":"9380624929474dbc"},{"name":"module-hash","duration":4622,"timestamp":2828737268198,"id":308,"parentId":301,"tags":{},"startTime":1753787666076,"traceId":"9380624929474dbc"},{"name":"code-generation","duration":37361,"timestamp":2828737275426,"id":309,"parentId":301,"tags":{},"startTime":1753787666083,"traceId":"9380624929474dbc"},{"name":"hash","duration":92804,"timestamp":2828737335199,"id":310,"parentId":301,"tags":{},"startTime":1753787666143,"traceId":"9380624929474dbc"},{"name":"code-generation-jobs","duration":3948,"timestamp":2828737427994,"id":311,"parentId":301,"tags":{},"startTime":1753787666236,"traceId":"9380624929474dbc"},{"name":"module-assets","duration":1190,"timestamp":2828737431849,"id":312,"parentId":301,"tags":{},"startTime":1753787666240,"traceId":"9380624929474dbc"},{"name":"create-chunk-assets","duration":34558,"timestamp":2828737433106,"id":313,"parentId":301,"tags":{},"startTime":1753787666241,"traceId":"9380624929474dbc"},{"name":"NextJsBuildManifest-generateClientManifest","duration":3267,"timestamp":2828737493096,"id":315,"parentId":286,"tags":{},"startTime":1753787666301,"traceId":"9380624929474dbc"},{"name":"NextJsBuildManifest-createassets","duration":5686,"timestamp":2828737490720,"id":314,"parentId":286,"tags":{},"startTime":1753787666299,"traceId":"9380624929474dbc"},{"name":"seal","duration":374816,"timestamp":2828737140845,"id":301,"parentId":286,"tags":{},"startTime":1753787665949,"traceId":"9380624929474dbc"},{"name":"webpack-compilation","duration":2168829,"timestamp":2828735348142,"id":286,"parentId":285,"tags":{"name":"client"},"startTime":1753787664156,"traceId":"9380624929474dbc"},{"name":"emit","duration":94650,"timestamp":2828737518993,"id":316,"parentId":285,"tags":{},"startTime":1753787666327,"traceId":"9380624929474dbc"},{"name":"webpack-invalidated-client","duration":2681884,"timestamp":2828734964302,"id":285,"parentId":3,"tags":{"trigger":"public/niqabi-aesthetic.jpg"},"startTime":1753787663772,"traceId":"9380624929474dbc"}]
[{"name":"add-entry","duration":103215,"timestamp":2828737715912,"id":320,"parentId":319,"tags":{"request":"D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1753787666524,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":196811,"timestamp":2828737716490,"id":324,"parentId":319,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1753787666524,"traceId":"9380624929474dbc"},{"name":"read-resource","duration":82794,"timestamp":2828737845187,"id":328,"parentId":327,"tags":{},"startTime":1753787666653,"traceId":"9380624929474dbc"},{"name":"postcss-process","duration":670315,"timestamp":2828737930043,"id":330,"parentId":329,"tags":{},"startTime":1753787666738,"traceId":"9380624929474dbc"},{"name":"postcss-loader","duration":671795,"timestamp":2828737929973,"id":329,"parentId":327,"tags":{},"startTime":1753787666738,"traceId":"9380624929474dbc"},{"name":"css-loader","duration":277578,"timestamp":2828738602001,"id":331,"parentId":327,"tags":{"astUsed":"true"},"startTime":1753787667410,"traceId":"9380624929474dbc"},{"name":"build-module-css","duration":1066230,"timestamp":2828737843901,"id":327,"parentId":326,"tags":{"name":"D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\app\\globals.css.webpack[javascript/auto]!=!D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[14].oneOf[12].use[2]!D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[14].oneOf[12].use[3]!D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\app\\globals.css","layer":null},"startTime":1753787666652,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":1204297,"timestamp":2828737716523,"id":325,"parentId":319,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Capp%5C%5Ccontact%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1753787666524,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":1206530,"timestamp":2828737716381,"id":321,"parentId":319,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1753787666524,"traceId":"9380624929474dbc"},{"name":"build-module-css","duration":1170267,"timestamp":2828737773673,"id":326,"parentId":318,"tags":{"name":"D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\app\\globals.css","layer":"app-pages-browser"},"startTime":1753787666582,"traceId":"9380624929474dbc"},{"name":"build-module","duration":220,"timestamp":2828738964694,"id":332,"parentId":326,"tags":{},"startTime":1753787667773,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":1252418,"timestamp":2828737716463,"id":323,"parentId":319,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1753787666524,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":1255064,"timestamp":2828737716433,"id":322,"parentId":319,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1753787666524,"traceId":"9380624929474dbc"},{"name":"make","duration":1257800,"timestamp":2828737713758,"id":319,"parentId":318,"tags":{},"startTime":1753787666522,"traceId":"9380624929474dbc"},{"name":"chunk-graph","duration":7589,"timestamp":2828739007112,"id":334,"parentId":333,"tags":{},"startTime":1753787667815,"traceId":"9380624929474dbc"},{"name":"optimize-modules","duration":11,"timestamp":2828739014812,"id":336,"parentId":333,"tags":{},"startTime":1753787667823,"traceId":"9380624929474dbc"},{"name":"optimize-chunks","duration":570,"timestamp":2828739015014,"id":337,"parentId":333,"tags":{},"startTime":1753787667823,"traceId":"9380624929474dbc"},{"name":"optimize-tree","duration":59,"timestamp":2828739015800,"id":338,"parentId":333,"tags":{},"startTime":1753787667824,"traceId":"9380624929474dbc"},{"name":"optimize-chunk-modules","duration":83,"timestamp":2828739016222,"id":339,"parentId":333,"tags":{},"startTime":1753787667824,"traceId":"9380624929474dbc"},{"name":"optimize","duration":23537,"timestamp":2828739014789,"id":335,"parentId":333,"tags":{},"startTime":1753787667823,"traceId":"9380624929474dbc"},{"name":"module-hash","duration":1010,"timestamp":2828739041318,"id":340,"parentId":333,"tags":{},"startTime":1753787667849,"traceId":"9380624929474dbc"},{"name":"code-generation","duration":6302,"timestamp":2828739042357,"id":341,"parentId":333,"tags":{},"startTime":1753787667850,"traceId":"9380624929474dbc"},{"name":"hash","duration":11422,"timestamp":2828739054291,"id":342,"parentId":333,"tags":{},"startTime":1753787667862,"traceId":"9380624929474dbc"},{"name":"code-generation-jobs","duration":299,"timestamp":2828739065710,"id":343,"parentId":333,"tags":{},"startTime":1753787667874,"traceId":"9380624929474dbc"},{"name":"module-assets","duration":494,"timestamp":2828739065981,"id":344,"parentId":333,"tags":{},"startTime":1753787667874,"traceId":"9380624929474dbc"},{"name":"create-chunk-assets","duration":20442,"timestamp":2828739066494,"id":345,"parentId":333,"tags":{},"startTime":1753787667874,"traceId":"9380624929474dbc"},{"name":"NextJsBuildManifest-generateClientManifest","duration":316,"timestamp":2828739090190,"id":347,"parentId":318,"tags":{},"startTime":1753787667898,"traceId":"9380624929474dbc"},{"name":"NextJsBuildManifest-createassets","duration":650,"timestamp":2828739089884,"id":346,"parentId":318,"tags":{},"startTime":1753787667898,"traceId":"9380624929474dbc"},{"name":"seal","duration":111321,"timestamp":2828738997464,"id":333,"parentId":318,"tags":{},"startTime":1753787667805,"traceId":"9380624929474dbc"},{"name":"webpack-compilation","duration":1398089,"timestamp":2828737710887,"id":318,"parentId":317,"tags":{"name":"client"},"startTime":1753787666519,"traceId":"9380624929474dbc"},{"name":"client-success","duration":31,"timestamp":2828739129554,"id":349,"parentId":3,"tags":{},"startTime":1753787667937,"traceId":"9380624929474dbc"},{"name":"emit","duration":66166,"timestamp":2828739109070,"id":348,"parentId":317,"tags":{},"startTime":1753787667917,"traceId":"9380624929474dbc"},{"name":"webpack-invalidated-client","duration":1497689,"timestamp":2828737681122,"id":317,"parentId":3,"tags":{"trigger":"app"},"startTime":1753787666489,"traceId":"9380624929474dbc"}]
[{"name":"client-success","duration":6,"timestamp":2828739227551,"id":351,"parentId":3,"tags":{},"startTime":1753787668035,"traceId":"9380624929474dbc"},{"name":"handle-request","duration":84285,"timestamp":2828739148969,"id":350,"tags":{"url":"/contact?_rsc=1rc55","isTurbopack":false},"startTime":1753787667957,"traceId":"9380624929474dbc"},{"name":"memory-usage","duration":3,"timestamp":2828739233676,"id":352,"parentId":350,"tags":{"url":"/contact?_rsc=1rc55","memory.rss":"256479232","memory.heapUsed":"214526048","memory.heapTotal":"249049088"},"startTime":1753787668042,"traceId":"9380624929474dbc"},{"name":"client-hmr-latency","duration":135000,"timestamp":2828739126406,"id":354,"parentId":3,"tags":{"updatedModules":[],"page":"/contact","isPageHidden":true},"startTime":1753787668070,"traceId":"9380624929474dbc"},{"name":"handle-request","duration":26304,"timestamp":2828739253166,"id":353,"tags":{"url":"/contact?_rsc=1rc55","isTurbopack":false},"startTime":1753787668061,"traceId":"9380624929474dbc"},{"name":"memory-usage","duration":7,"timestamp":2828739279548,"id":355,"parentId":353,"tags":{"url":"/contact?_rsc=1rc55","memory.rss":"256552960","memory.heapUsed":"215514688","memory.heapTotal":"249049088"},"startTime":1753787668087,"traceId":"9380624929474dbc"},{"name":"client-hmr-latency","duration":206000,"timestamp":2828739126184,"id":356,"parentId":3,"tags":{"updatedModules":[],"page":"/contact","isPageHidden":true},"startTime":1753787668140,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":39648,"timestamp":2828740806004,"id":360,"parentId":359,"tags":{"request":"D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1753787669614,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":89459,"timestamp":2828740806155,"id":364,"parentId":359,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1753787669614,"traceId":"9380624929474dbc"},{"name":"read-resource","duration":41895,"timestamp":2828740854019,"id":368,"parentId":367,"tags":{},"startTime":1753787669662,"traceId":"9380624929474dbc"},{"name":"postcss-process","duration":168819,"timestamp":2828740896349,"id":370,"parentId":369,"tags":{},"startTime":1753787669704,"traceId":"9380624929474dbc"},{"name":"postcss-loader","duration":169972,"timestamp":2828740896021,"id":369,"parentId":367,"tags":{},"startTime":1753787669704,"traceId":"9380624929474dbc"},{"name":"css-loader","duration":65712,"timestamp":2828741066139,"id":371,"parentId":367,"tags":{"astUsed":"true"},"startTime":1753787669874,"traceId":"9380624929474dbc"},{"name":"build-module-css","duration":284245,"timestamp":2828740853318,"id":367,"parentId":366,"tags":{"name":"D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\app\\globals.css.webpack[javascript/auto]!=!D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[14].oneOf[12].use[2]!D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[14].oneOf[12].use[3]!D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\app\\globals.css","layer":null},"startTime":1753787669661,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":334105,"timestamp":2828740806163,"id":365,"parentId":359,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Capp%5C%5Ccontact%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1753787669614,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":342797,"timestamp":2828740806129,"id":361,"parentId":359,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1753787669614,"traceId":"9380624929474dbc"},{"name":"build-module-css","duration":329501,"timestamp":2828740839400,"id":366,"parentId":358,"tags":{"name":"D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\app\\globals.css","layer":"app-pages-browser"},"startTime":1753787669647,"traceId":"9380624929474dbc"},{"name":"build-module","duration":144,"timestamp":2828741171435,"id":372,"parentId":366,"tags":{},"startTime":1753787669979,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":365978,"timestamp":2828740806149,"id":363,"parentId":359,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1753787669614,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":383773,"timestamp":2828740806142,"id":362,"parentId":359,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1753787669614,"traceId":"9380624929474dbc"},{"name":"make","duration":385438,"timestamp":2828740804544,"id":359,"parentId":358,"tags":{},"startTime":1753787669612,"traceId":"9380624929474dbc"},{"name":"chunk-graph","duration":4132,"timestamp":2828741244146,"id":374,"parentId":373,"tags":{},"startTime":1753787670052,"traceId":"9380624929474dbc"},{"name":"optimize-modules","duration":18,"timestamp":2828741248387,"id":376,"parentId":373,"tags":{},"startTime":1753787670056,"traceId":"9380624929474dbc"},{"name":"optimize-chunks","duration":198,"timestamp":2828741248453,"id":377,"parentId":373,"tags":{},"startTime":1753787670056,"traceId":"9380624929474dbc"},{"name":"optimize-tree","duration":40,"timestamp":2828741248724,"id":378,"parentId":373,"tags":{},"startTime":1753787670057,"traceId":"9380624929474dbc"},{"name":"optimize-chunk-modules","duration":18,"timestamp":2828741248811,"id":379,"parentId":373,"tags":{},"startTime":1753787670057,"traceId":"9380624929474dbc"},{"name":"optimize","duration":4430,"timestamp":2828741248353,"id":375,"parentId":373,"tags":{},"startTime":1753787670056,"traceId":"9380624929474dbc"},{"name":"module-hash","duration":2096,"timestamp":2828741270421,"id":380,"parentId":373,"tags":{},"startTime":1753787670078,"traceId":"9380624929474dbc"},{"name":"code-generation","duration":6187,"timestamp":2828741272571,"id":381,"parentId":373,"tags":{},"startTime":1753787670080,"traceId":"9380624929474dbc"},{"name":"hash","duration":16159,"timestamp":2828741283397,"id":382,"parentId":373,"tags":{},"startTime":1753787670091,"traceId":"9380624929474dbc"},{"name":"code-generation-jobs","duration":1928,"timestamp":2828741299548,"id":383,"parentId":373,"tags":{},"startTime":1753787670107,"traceId":"9380624929474dbc"},{"name":"module-assets","duration":717,"timestamp":2828741301415,"id":384,"parentId":373,"tags":{},"startTime":1753787670109,"traceId":"9380624929474dbc"},{"name":"create-chunk-assets","duration":20371,"timestamp":2828741302185,"id":385,"parentId":373,"tags":{},"startTime":1753787670110,"traceId":"9380624929474dbc"},{"name":"NextJsBuildManifest-generateClientManifest","duration":169,"timestamp":2828741327247,"id":387,"parentId":358,"tags":{},"startTime":1753787670135,"traceId":"9380624929474dbc"},{"name":"NextJsBuildManifest-createassets","duration":459,"timestamp":2828741326983,"id":386,"parentId":358,"tags":{},"startTime":1753787670135,"traceId":"9380624929474dbc"},{"name":"seal","duration":115011,"timestamp":2828741218154,"id":373,"parentId":358,"tags":{},"startTime":1753787670026,"traceId":"9380624929474dbc"},{"name":"webpack-compilation","duration":530181,"timestamp":2828740803132,"id":358,"parentId":357,"tags":{"name":"client"},"startTime":1753787669611,"traceId":"9380624929474dbc"},{"name":"emit","duration":55405,"timestamp":2828741333415,"id":388,"parentId":357,"tags":{},"startTime":1753787670141,"traceId":"9380624929474dbc"},{"name":"webpack-invalidated-client","duration":610904,"timestamp":2828740783391,"id":357,"parentId":3,"tags":{"trigger":"public/placeholder.svg"},"startTime":1753787669591,"traceId":"9380624929474dbc"}]
[{"name":"client-success","duration":17,"timestamp":2828741402665,"id":389,"parentId":3,"tags":{},"startTime":1753787670211,"traceId":"9380624929474dbc"},{"name":"client-hmr-latency","duration":677000,"timestamp":2828740792126,"id":391,"parentId":3,"tags":{"updatedModules":[],"page":"/contact","isPageHidden":true},"startTime":1753787670300,"traceId":"9380624929474dbc"},{"name":"handle-request","duration":91746,"timestamp":2828741406099,"id":390,"tags":{"url":"/contact?_rsc=1rc55","isTurbopack":false},"startTime":1753787670214,"traceId":"9380624929474dbc"},{"name":"memory-usage","duration":8,"timestamp":2828741498034,"id":392,"parentId":390,"tags":{"url":"/contact?_rsc=1rc55","memory.rss":"265969664","memory.heapUsed":"231190072","memory.heapTotal":"258224128"},"startTime":1753787670306,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":46387,"timestamp":2828742076997,"id":396,"parentId":395,"tags":{"request":"D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1753787670885,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":83048,"timestamp":2828742077566,"id":400,"parentId":395,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1753787670885,"traceId":"9380624929474dbc"},{"name":"read-resource","duration":28922,"timestamp":2828742132038,"id":404,"parentId":403,"tags":{},"startTime":1753787670940,"traceId":"9380624929474dbc"},{"name":"postcss-process","duration":219132,"timestamp":2828742161273,"id":406,"parentId":405,"tags":{},"startTime":1753787670969,"traceId":"9380624929474dbc"},{"name":"postcss-loader","duration":220034,"timestamp":2828742161167,"id":405,"parentId":403,"tags":{},"startTime":1753787670969,"traceId":"9380624929474dbc"},{"name":"css-loader","duration":62839,"timestamp":2828742381361,"id":407,"parentId":403,"tags":{"astUsed":"true"},"startTime":1753787671189,"traceId":"9380624929474dbc"},{"name":"build-module-css","duration":343298,"timestamp":2828742131413,"id":403,"parentId":402,"tags":{"name":"D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\app\\globals.css.webpack[javascript/auto]!=!D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[14].oneOf[12].use[2]!D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[14].oneOf[12].use[3]!D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\app\\globals.css","layer":null},"startTime":1753787670939,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":399977,"timestamp":2828742077575,"id":401,"parentId":395,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Capp%5C%5Ccontact%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1753787670885,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":408416,"timestamp":2828742077125,"id":397,"parentId":395,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1753787670885,"traceId":"9380624929474dbc"},{"name":"build-module-css","duration":407736,"timestamp":2828742115588,"id":402,"parentId":394,"tags":{"name":"D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\app\\globals.css","layer":"app-pages-browser"},"startTime":1753787670923,"traceId":"9380624929474dbc"},{"name":"build-module","duration":1091,"timestamp":2828742550197,"id":408,"parentId":402,"tags":{},"startTime":1753787671358,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":476667,"timestamp":2828742077538,"id":399,"parentId":395,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1753787670885,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":482612,"timestamp":2828742077362,"id":398,"parentId":395,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1753787670885,"traceId":"9380624929474dbc"},{"name":"make","duration":483845,"timestamp":2828742076277,"id":395,"parentId":394,"tags":{},"startTime":1753787670884,"traceId":"9380624929474dbc"},{"name":"chunk-graph","duration":6509,"timestamp":2828742628845,"id":410,"parentId":409,"tags":{},"startTime":1753787671437,"traceId":"9380624929474dbc"},{"name":"optimize-modules","duration":27,"timestamp":2828742635550,"id":412,"parentId":409,"tags":{},"startTime":1753787671443,"traceId":"9380624929474dbc"},{"name":"optimize-chunks","duration":340,"timestamp":2828742635650,"id":413,"parentId":409,"tags":{},"startTime":1753787671443,"traceId":"9380624929474dbc"},{"name":"optimize-tree","duration":33,"timestamp":2828742636093,"id":414,"parentId":409,"tags":{},"startTime":1753787671444,"traceId":"9380624929474dbc"},{"name":"optimize-chunk-modules","duration":26,"timestamp":2828742636269,"id":415,"parentId":409,"tags":{},"startTime":1753787671444,"traceId":"9380624929474dbc"},{"name":"optimize","duration":7239,"timestamp":2828742635499,"id":411,"parentId":409,"tags":{},"startTime":1753787671443,"traceId":"9380624929474dbc"},{"name":"module-hash","duration":29009,"timestamp":2828742650727,"id":416,"parentId":409,"tags":{},"startTime":1753787671459,"traceId":"9380624929474dbc"},{"name":"code-generation","duration":14158,"timestamp":2828742679853,"id":417,"parentId":409,"tags":{},"startTime":1753787671488,"traceId":"9380624929474dbc"},{"name":"hash","duration":37757,"timestamp":2828742711314,"id":418,"parentId":409,"tags":{},"startTime":1753787671519,"traceId":"9380624929474dbc"},{"name":"code-generation-jobs","duration":815,"timestamp":2828742749065,"id":419,"parentId":409,"tags":{},"startTime":1753787671557,"traceId":"9380624929474dbc"},{"name":"module-assets","duration":1773,"timestamp":2828742749818,"id":420,"parentId":409,"tags":{},"startTime":1753787671558,"traceId":"9380624929474dbc"},{"name":"create-chunk-assets","duration":16242,"timestamp":2828742751676,"id":421,"parentId":409,"tags":{},"startTime":1753787671560,"traceId":"9380624929474dbc"},{"name":"NextJsBuildManifest-generateClientManifest","duration":97,"timestamp":2828742772864,"id":423,"parentId":394,"tags":{},"startTime":1753787671581,"traceId":"9380624929474dbc"},{"name":"NextJsBuildManifest-createassets","duration":253,"timestamp":2828742772723,"id":422,"parentId":394,"tags":{},"startTime":1753787671581,"traceId":"9380624929474dbc"},{"name":"seal","duration":174268,"timestamp":2828742604555,"id":409,"parentId":394,"tags":{},"startTime":1753787671412,"traceId":"9380624929474dbc"},{"name":"webpack-compilation","duration":703662,"timestamp":2828742075320,"id":394,"parentId":393,"tags":{"name":"client"},"startTime":1753787670883,"traceId":"9380624929474dbc"},{"name":"emit","duration":39675,"timestamp":2828742779065,"id":424,"parentId":393,"tags":{},"startTime":1753787671587,"traceId":"9380624929474dbc"},{"name":"webpack-invalidated-client","duration":777466,"timestamp":2828742047938,"id":393,"parentId":3,"tags":{"trigger":"public/placeholder-logo.png"},"startTime":1753787670856,"traceId":"9380624929474dbc"}]
[{"name":"client-success","duration":11,"timestamp":2828742837663,"id":426,"parentId":3,"tags":{},"startTime":1753787671646,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":37045,"timestamp":2828742854364,"id":429,"parentId":428,"tags":{"request":"D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1753787671662,"traceId":"9380624929474dbc"},{"name":"client-hmr-latency","duration":67000,"timestamp":2828742838125,"id":439,"parentId":3,"tags":{"updatedModules":[],"page":"/contact","isPageHidden":true},"startTime":1753787671715,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":53522,"timestamp":2828742854578,"id":433,"parentId":428,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1753787671662,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":56287,"timestamp":2828742854603,"id":434,"parentId":428,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Capp%5C%5Ccontact%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1753787671662,"traceId":"9380624929474dbc"},{"name":"read-resource","duration":12682,"timestamp":2828742898616,"id":438,"parentId":437,"tags":{},"startTime":1753787671706,"traceId":"9380624929474dbc"},{"name":"postcss-process","duration":142245,"timestamp":2828742911485,"id":441,"parentId":440,"tags":{},"startTime":1753787671719,"traceId":"9380624929474dbc"},{"name":"postcss-loader","duration":143018,"timestamp":2828742911399,"id":440,"parentId":437,"tags":{},"startTime":1753787671719,"traceId":"9380624929474dbc"},{"name":"css-loader","duration":76941,"timestamp":2828743054492,"id":442,"parentId":437,"tags":{"astUsed":"true"},"startTime":1753787671862,"traceId":"9380624929474dbc"},{"name":"build-module-css","duration":246404,"timestamp":2828742897888,"id":437,"parentId":436,"tags":{"name":"D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\app\\globals.css.webpack[javascript/auto]!=!D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[14].oneOf[12].use[2]!D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[14].oneOf[12].use[3]!D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\app\\globals.css","layer":null},"startTime":1753787671706,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":301236,"timestamp":2828742854521,"id":430,"parentId":428,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1753787671662,"traceId":"9380624929474dbc"},{"name":"build-module-css","duration":294655,"timestamp":2828742879021,"id":436,"parentId":427,"tags":{"name":"D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\app\\globals.css","layer":"app-pages-browser"},"startTime":1753787671687,"traceId":"9380624929474dbc"},{"name":"build-module","duration":222,"timestamp":2828743192955,"id":443,"parentId":436,"tags":{},"startTime":1753787672001,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":346696,"timestamp":2828742854563,"id":432,"parentId":428,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1753787671662,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":349710,"timestamp":2828742854546,"id":431,"parentId":428,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1753787671662,"traceId":"9380624929474dbc"},{"name":"make","duration":351023,"timestamp":2828742853340,"id":428,"parentId":427,"tags":{},"startTime":1753787671661,"traceId":"9380624929474dbc"},{"name":"chunk-graph","duration":5963,"timestamp":2828743261884,"id":445,"parentId":444,"tags":{},"startTime":1753787672070,"traceId":"9380624929474dbc"},{"name":"optimize-modules","duration":18,"timestamp":2828743267963,"id":447,"parentId":444,"tags":{},"startTime":1753787672076,"traceId":"9380624929474dbc"},{"name":"optimize-chunks","duration":188,"timestamp":2828743268029,"id":448,"parentId":444,"tags":{},"startTime":1753787672076,"traceId":"9380624929474dbc"},{"name":"optimize-tree","duration":21,"timestamp":2828743268287,"id":449,"parentId":444,"tags":{},"startTime":1753787672076,"traceId":"9380624929474dbc"},{"name":"optimize-chunk-modules","duration":15,"timestamp":2828743268374,"id":450,"parentId":444,"tags":{},"startTime":1753787672076,"traceId":"9380624929474dbc"},{"name":"optimize","duration":4755,"timestamp":2828743267928,"id":446,"parentId":444,"tags":{},"startTime":1753787672076,"traceId":"9380624929474dbc"},{"name":"module-hash","duration":1076,"timestamp":2828743276354,"id":451,"parentId":444,"tags":{},"startTime":1753787672084,"traceId":"9380624929474dbc"},{"name":"code-generation","duration":7282,"timestamp":2828743277538,"id":452,"parentId":444,"tags":{},"startTime":1753787672085,"traceId":"9380624929474dbc"},{"name":"hash","duration":15432,"timestamp":2828743289184,"id":453,"parentId":444,"tags":{},"startTime":1753787672097,"traceId":"9380624929474dbc"},{"name":"code-generation-jobs","duration":462,"timestamp":2828743304611,"id":454,"parentId":444,"tags":{},"startTime":1753787672112,"traceId":"9380624929474dbc"},{"name":"module-assets","duration":429,"timestamp":2828743305040,"id":455,"parentId":444,"tags":{},"startTime":1753787672113,"traceId":"9380624929474dbc"},{"name":"create-chunk-assets","duration":19905,"timestamp":2828743305498,"id":456,"parentId":444,"tags":{},"startTime":1753787672113,"traceId":"9380624929474dbc"},{"name":"NextJsBuildManifest-generateClientManifest","duration":1412,"timestamp":2828743328856,"id":458,"parentId":427,"tags":{},"startTime":1753787672137,"traceId":"9380624929474dbc"},{"name":"NextJsBuildManifest-createassets","duration":1938,"timestamp":2828743328647,"id":457,"parentId":427,"tags":{},"startTime":1753787672136,"traceId":"9380624929474dbc"},{"name":"seal","duration":107700,"timestamp":2828743227556,"id":444,"parentId":427,"tags":{},"startTime":1753787672035,"traceId":"9380624929474dbc"},{"name":"webpack-compilation","duration":483152,"timestamp":2828742852242,"id":427,"parentId":425,"tags":{"name":"client"},"startTime":1753787671660,"traceId":"9380624929474dbc"},{"name":"handle-request","duration":501849,"timestamp":2828742858094,"id":435,"tags":{"url":"/contact?_rsc=1rc55","isTurbopack":false},"startTime":1753787671666,"traceId":"9380624929474dbc"},{"name":"memory-usage","duration":9,"timestamp":2828743360066,"id":460,"parentId":435,"tags":{"url":"/contact?_rsc=1rc55","memory.rss":"286056448","memory.heapUsed":"243717632","memory.heapTotal":"280244224"},"startTime":1753787672168,"traceId":"9380624929474dbc"},{"name":"emit","duration":100266,"timestamp":2828743335469,"id":459,"parentId":425,"tags":{},"startTime":1753787672143,"traceId":"9380624929474dbc"},{"name":"webpack-invalidated-client","duration":616176,"timestamp":2828742836328,"id":425,"parentId":3,"tags":{"trigger":""},"startTime":1753787671644,"traceId":"9380624929474dbc"}]
[{"name":"add-entry","duration":48430,"timestamp":2828743500805,"id":464,"parentId":463,"tags":{"request":"D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1753787672309,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":79561,"timestamp":2828743501206,"id":468,"parentId":463,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1753787672309,"traceId":"9380624929474dbc"},{"name":"read-resource","duration":19666,"timestamp":2828743561814,"id":472,"parentId":471,"tags":{},"startTime":1753787672370,"traceId":"9380624929474dbc"},{"name":"postcss-process","duration":358272,"timestamp":2828743582483,"id":474,"parentId":473,"tags":{},"startTime":1753787672390,"traceId":"9380624929474dbc"},{"name":"postcss-loader","duration":359764,"timestamp":2828743582386,"id":473,"parentId":471,"tags":{},"startTime":1753787672390,"traceId":"9380624929474dbc"},{"name":"css-loader","duration":78092,"timestamp":2828743942333,"id":475,"parentId":471,"tags":{"astUsed":"true"},"startTime":1753787672750,"traceId":"9380624929474dbc"},{"name":"build-module-css","duration":470655,"timestamp":2828743560994,"id":471,"parentId":470,"tags":{"name":"D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\app\\globals.css.webpack[javascript/auto]!=!D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[14].oneOf[12].use[2]!D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[14].oneOf[12].use[3]!D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\app\\globals.css","layer":null},"startTime":1753787672369,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":533884,"timestamp":2828743501264,"id":469,"parentId":463,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Capp%5C%5Ccontact%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1753787672309,"traceId":"9380624929474dbc"},{"name":"client-success","duration":13,"timestamp":2828744038551,"id":476,"parentId":3,"tags":{},"startTime":1753787672846,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":548108,"timestamp":2828743501080,"id":465,"parentId":463,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1753787672309,"traceId":"9380624929474dbc"},{"name":"build-module-css","duration":529977,"timestamp":2828743528676,"id":470,"parentId":462,"tags":{"name":"D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\app\\globals.css","layer":"app-pages-browser"},"startTime":1753787672337,"traceId":"9380624929474dbc"},{"name":"build-module","duration":211,"timestamp":2828744071210,"id":478,"parentId":470,"tags":{},"startTime":1753787672879,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":575993,"timestamp":2828743501171,"id":467,"parentId":463,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1753787672309,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":580382,"timestamp":2828743501135,"id":466,"parentId":463,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1753787672309,"traceId":"9380624929474dbc"},{"name":"make","duration":583828,"timestamp":2828743497821,"id":463,"parentId":462,"tags":{},"startTime":1753787672306,"traceId":"9380624929474dbc"},{"name":"chunk-graph","duration":7627,"timestamp":2828744124987,"id":480,"parentId":479,"tags":{},"startTime":1753787672933,"traceId":"9380624929474dbc"},{"name":"optimize-modules","duration":40,"timestamp":2828744132774,"id":482,"parentId":479,"tags":{},"startTime":1753787672941,"traceId":"9380624929474dbc"},{"name":"optimize-chunks","duration":344,"timestamp":2828744132889,"id":483,"parentId":479,"tags":{},"startTime":1753787672941,"traceId":"9380624929474dbc"},{"name":"optimize-tree","duration":52,"timestamp":2828744133415,"id":484,"parentId":479,"tags":{},"startTime":1753787672941,"traceId":"9380624929474dbc"},{"name":"optimize-chunk-modules","duration":31,"timestamp":2828744133582,"id":485,"parentId":479,"tags":{},"startTime":1753787672941,"traceId":"9380624929474dbc"},{"name":"optimize","duration":12775,"timestamp":2828744132724,"id":481,"parentId":479,"tags":{},"startTime":1753787672941,"traceId":"9380624929474dbc"},{"name":"module-hash","duration":2485,"timestamp":2828744159156,"id":486,"parentId":479,"tags":{},"startTime":1753787672967,"traceId":"9380624929474dbc"},{"name":"code-generation","duration":9337,"timestamp":2828744161793,"id":487,"parentId":479,"tags":{},"startTime":1753787672970,"traceId":"9380624929474dbc"},{"name":"hash","duration":25638,"timestamp":2828744177826,"id":488,"parentId":479,"tags":{},"startTime":1753787672986,"traceId":"9380624929474dbc"},{"name":"code-generation-jobs","duration":567,"timestamp":2828744203457,"id":489,"parentId":479,"tags":{},"startTime":1753787673011,"traceId":"9380624929474dbc"},{"name":"module-assets","duration":426,"timestamp":2828744203988,"id":490,"parentId":479,"tags":{},"startTime":1753787673012,"traceId":"9380624929474dbc"},{"name":"create-chunk-assets","duration":32132,"timestamp":2828744204437,"id":491,"parentId":479,"tags":{},"startTime":1753787673012,"traceId":"9380624929474dbc"},{"name":"NextJsBuildManifest-generateClientManifest","duration":420,"timestamp":2828744243014,"id":493,"parentId":462,"tags":{},"startTime":1753787673051,"traceId":"9380624929474dbc"},{"name":"NextJsBuildManifest-createassets","duration":992,"timestamp":2828744242488,"id":492,"parentId":462,"tags":{},"startTime":1753787673050,"traceId":"9380624929474dbc"},{"name":"seal","duration":148306,"timestamp":2828744101915,"id":479,"parentId":462,"tags":{},"startTime":1753787672910,"traceId":"9380624929474dbc"},{"name":"webpack-compilation","duration":757165,"timestamp":2828743493268,"id":462,"parentId":461,"tags":{"name":"client"},"startTime":1753787672301,"traceId":"9380624929474dbc"},{"name":"emit","duration":49849,"timestamp":2828744250536,"id":494,"parentId":461,"tags":{},"startTime":1753787673058,"traceId":"9380624929474dbc"},{"name":"webpack-invalidated-client","duration":832401,"timestamp":2828743473602,"id":461,"parentId":3,"tags":{"trigger":""},"startTime":1753787672281,"traceId":"9380624929474dbc"}]
[{"name":"client-hmr-latency","duration":562000,"timestamp":2828743736018,"id":495,"parentId":3,"tags":{"updatedModules":[],"page":"/contact","isPageHidden":true},"startTime":1753787673124,"traceId":"9380624929474dbc"},{"name":"client-success","duration":6,"timestamp":2828744316130,"id":496,"parentId":3,"tags":{},"startTime":1753787673124,"traceId":"9380624929474dbc"},{"name":"handle-request","duration":279053,"timestamp":2828744059918,"id":477,"tags":{"url":"/contact?_rsc=1rc55","isTurbopack":false},"startTime":1753787672868,"traceId":"9380624929474dbc"},{"name":"memory-usage","duration":5,"timestamp":2828744339054,"id":497,"parentId":477,"tags":{"url":"/contact?_rsc=1rc55","memory.rss":"281829376","memory.heapUsed":"231842424","memory.heapTotal":"283725824"},"startTime":1753787673147,"traceId":"9380624929474dbc"},{"name":"client-hmr-latency","duration":621000,"timestamp":2828743736443,"id":499,"parentId":3,"tags":{"updatedModules":[],"page":"/contact","isPageHidden":true},"startTime":1753787673166,"traceId":"9380624929474dbc"},{"name":"handle-request","duration":25406,"timestamp":2828744348185,"id":498,"tags":{"url":"/contact?_rsc=1rc55","isTurbopack":false},"startTime":1753787673156,"traceId":"9380624929474dbc"},{"name":"memory-usage","duration":8,"timestamp":2828744373770,"id":500,"parentId":498,"tags":{"url":"/contact?_rsc=1rc55","memory.rss":"281829376","memory.heapUsed":"232848488","memory.heapTotal":"283725824"},"startTime":1753787673182,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":17155,"timestamp":2828745100906,"id":504,"parentId":503,"tags":{"request":"D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1753787673909,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":45932,"timestamp":2828745101126,"id":508,"parentId":503,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1753787673909,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":50737,"timestamp":2828745101144,"id":509,"parentId":503,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Capp%5C%5Ccontact%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1753787673909,"traceId":"9380624929474dbc"},{"name":"read-resource","duration":12192,"timestamp":2828745140560,"id":512,"parentId":511,"tags":{},"startTime":1753787673948,"traceId":"9380624929474dbc"},{"name":"postcss-process","duration":136843,"timestamp":2828745153070,"id":514,"parentId":513,"tags":{},"startTime":1753787673961,"traceId":"9380624929474dbc"},{"name":"postcss-loader","duration":137716,"timestamp":2828745152965,"id":513,"parentId":511,"tags":{},"startTime":1753787673961,"traceId":"9380624929474dbc"},{"name":"css-loader","duration":54429,"timestamp":2828745290804,"id":515,"parentId":511,"tags":{"astUsed":"true"},"startTime":1753787674099,"traceId":"9380624929474dbc"},{"name":"build-module-css","duration":210010,"timestamp":2828745139979,"id":511,"parentId":510,"tags":{"name":"D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\app\\globals.css.webpack[javascript/auto]!=!D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[14].oneOf[12].use[2]!D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[14].oneOf[12].use[3]!D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\app\\globals.css","layer":null},"startTime":1753787673948,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":264433,"timestamp":2828745101066,"id":505,"parentId":503,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1753787673909,"traceId":"9380624929474dbc"},{"name":"build-module-css","duration":265055,"timestamp":2828745112124,"id":510,"parentId":502,"tags":{"name":"D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\app\\globals.css","layer":"app-pages-browser"},"startTime":1753787673920,"traceId":"9380624929474dbc"},{"name":"build-module","duration":240,"timestamp":2828745382273,"id":516,"parentId":510,"tags":{},"startTime":1753787674190,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":283364,"timestamp":2828745101111,"id":507,"parentId":503,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1753787673909,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":284274,"timestamp":2828745101094,"id":506,"parentId":503,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1753787673909,"traceId":"9380624929474dbc"},{"name":"make","duration":285628,"timestamp":2828745099816,"id":503,"parentId":502,"tags":{},"startTime":1753787673908,"traceId":"9380624929474dbc"},{"name":"chunk-graph","duration":8070,"timestamp":2828745434212,"id":518,"parentId":517,"tags":{},"startTime":1753787674242,"traceId":"9380624929474dbc"},{"name":"optimize-modules","duration":20,"timestamp":2828745442409,"id":520,"parentId":517,"tags":{},"startTime":1753787674250,"traceId":"9380624929474dbc"},{"name":"optimize-chunks","duration":202,"timestamp":2828745442474,"id":521,"parentId":517,"tags":{},"startTime":1753787674250,"traceId":"9380624929474dbc"},{"name":"optimize-tree","duration":23,"timestamp":2828745442742,"id":522,"parentId":517,"tags":{},"startTime":1753787674251,"traceId":"9380624929474dbc"},{"name":"optimize-chunk-modules","duration":14,"timestamp":2828745442842,"id":523,"parentId":517,"tags":{},"startTime":1753787674251,"traceId":"9380624929474dbc"},{"name":"optimize","duration":6096,"timestamp":2828745442374,"id":519,"parentId":517,"tags":{},"startTime":1753787674250,"traceId":"9380624929474dbc"},{"name":"module-hash","duration":1052,"timestamp":2828745457214,"id":524,"parentId":517,"tags":{},"startTime":1753787674265,"traceId":"9380624929474dbc"},{"name":"code-generation","duration":12658,"timestamp":2828745458325,"id":525,"parentId":517,"tags":{},"startTime":1753787674266,"traceId":"9380624929474dbc"},{"name":"hash","duration":20632,"timestamp":2828745494948,"id":526,"parentId":517,"tags":{},"startTime":1753787674303,"traceId":"9380624929474dbc"},{"name":"code-generation-jobs","duration":294,"timestamp":2828745515574,"id":527,"parentId":517,"tags":{},"startTime":1753787674323,"traceId":"9380624929474dbc"},{"name":"module-assets","duration":262,"timestamp":2828745515850,"id":528,"parentId":517,"tags":{},"startTime":1753787674324,"traceId":"9380624929474dbc"},{"name":"create-chunk-assets","duration":14667,"timestamp":2828745516140,"id":529,"parentId":517,"tags":{},"startTime":1753787674324,"traceId":"9380624929474dbc"},{"name":"NextJsBuildManifest-generateClientManifest","duration":281,"timestamp":2828745535444,"id":531,"parentId":502,"tags":{},"startTime":1753787674343,"traceId":"9380624929474dbc"},{"name":"NextJsBuildManifest-createassets","duration":645,"timestamp":2828745535123,"id":530,"parentId":502,"tags":{},"startTime":1753787674343,"traceId":"9380624929474dbc"},{"name":"seal","duration":131684,"timestamp":2828745411998,"id":517,"parentId":502,"tags":{},"startTime":1753787674220,"traceId":"9380624929474dbc"},{"name":"webpack-compilation","duration":445177,"timestamp":2828745098711,"id":502,"parentId":501,"tags":{"name":"client"},"startTime":1753787673907,"traceId":"9380624929474dbc"},{"name":"emit","duration":51997,"timestamp":2828745543953,"id":532,"parentId":501,"tags":{},"startTime":1753787674352,"traceId":"9380624929474dbc"},{"name":"webpack-invalidated-client","duration":528954,"timestamp":2828745071584,"id":501,"parentId":3,"tags":{"trigger":"public/profile.png"},"startTime":1753787673879,"traceId":"9380624929474dbc"}]
[{"name":"client-success","duration":12,"timestamp":2828745607873,"id":534,"parentId":3,"tags":{},"startTime":1753787674416,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":33323,"timestamp":2828745621974,"id":538,"parentId":537,"tags":{"request":"D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1753787674430,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":59609,"timestamp":2828745622185,"id":542,"parentId":537,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1753787674430,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":63808,"timestamp":2828745622197,"id":543,"parentId":537,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Capp%5C%5Ccontact%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1753787674430,"traceId":"9380624929474dbc"},{"name":"read-resource","duration":21719,"timestamp":2828745665082,"id":546,"parentId":545,"tags":{},"startTime":1753787674473,"traceId":"9380624929474dbc"},{"name":"postcss-process","duration":131911,"timestamp":2828745687129,"id":548,"parentId":547,"tags":{},"startTime":1753787674495,"traceId":"9380624929474dbc"},{"name":"postcss-loader","duration":132806,"timestamp":2828745686990,"id":547,"parentId":545,"tags":{},"startTime":1753787674495,"traceId":"9380624929474dbc"},{"name":"css-loader","duration":71944,"timestamp":2828745819921,"id":549,"parentId":545,"tags":{"astUsed":"true"},"startTime":1753787674628,"traceId":"9380624929474dbc"},{"name":"build-module-css","duration":234952,"timestamp":2828745664543,"id":545,"parentId":544,"tags":{"name":"D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\app\\globals.css.webpack[javascript/auto]!=!D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[14].oneOf[12].use[2]!D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[14].oneOf[12].use[3]!D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\app\\globals.css","layer":null},"startTime":1753787674472,"traceId":"9380624929474dbc"},{"name":"client-hmr-latency","duration":88000,"timestamp":2828745609185,"id":550,"parentId":3,"tags":{"updatedModules":[],"page":"/contact","isPageHidden":true},"startTime":1753787674751,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":324464,"timestamp":2828745622118,"id":539,"parentId":537,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1753787674430,"traceId":"9380624929474dbc"},{"name":"build-module-css","duration":317690,"timestamp":2828745646429,"id":544,"parentId":536,"tags":{"name":"D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\app\\globals.css","layer":"app-pages-browser"},"startTime":1753787674454,"traceId":"9380624929474dbc"},{"name":"build-module","duration":147,"timestamp":2828745970711,"id":551,"parentId":544,"tags":{},"startTime":1753787674779,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":351274,"timestamp":2828745622174,"id":541,"parentId":537,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1753787674430,"traceId":"9380624929474dbc"},{"name":"handle-request","duration":364133,"timestamp":2828745612714,"id":535,"tags":{"url":"/contact?_rsc=1rc55","isTurbopack":false},"startTime":1753787674421,"traceId":"9380624929474dbc"},{"name":"memory-usage","duration":6,"timestamp":2828745976925,"id":552,"parentId":535,"tags":{"url":"/contact?_rsc=1rc55","memory.rss":"284184576","memory.heapUsed":"248667648","memory.heapTotal":"283987968"},"startTime":1753787674785,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":354912,"timestamp":2828745622145,"id":540,"parentId":537,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1753787674430,"traceId":"9380624929474dbc"},{"name":"make","duration":356175,"timestamp":2828745620941,"id":537,"parentId":536,"tags":{},"startTime":1753787674429,"traceId":"9380624929474dbc"},{"name":"chunk-graph","duration":7299,"timestamp":2828746023202,"id":554,"parentId":553,"tags":{},"startTime":1753787674831,"traceId":"9380624929474dbc"},{"name":"optimize-modules","duration":52,"timestamp":2828746030642,"id":556,"parentId":553,"tags":{},"startTime":1753787674838,"traceId":"9380624929474dbc"},{"name":"optimize-chunks","duration":322,"timestamp":2828746030765,"id":557,"parentId":553,"tags":{},"startTime":1753787674839,"traceId":"9380624929474dbc"},{"name":"optimize-tree","duration":29,"timestamp":2828746031184,"id":558,"parentId":553,"tags":{},"startTime":1753787674839,"traceId":"9380624929474dbc"},{"name":"optimize-chunk-modules","duration":21,"timestamp":2828746031289,"id":559,"parentId":553,"tags":{},"startTime":1753787674839,"traceId":"9380624929474dbc"},{"name":"optimize","duration":6376,"timestamp":2828746030600,"id":555,"parentId":553,"tags":{},"startTime":1753787674838,"traceId":"9380624929474dbc"},{"name":"module-hash","duration":1986,"timestamp":2828746045776,"id":560,"parentId":553,"tags":{},"startTime":1753787674854,"traceId":"9380624929474dbc"},{"name":"code-generation","duration":9106,"timestamp":2828746047844,"id":561,"parentId":553,"tags":{},"startTime":1753787674856,"traceId":"9380624929474dbc"},{"name":"hash","duration":16118,"timestamp":2828746063636,"id":562,"parentId":553,"tags":{},"startTime":1753787674871,"traceId":"9380624929474dbc"},{"name":"code-generation-jobs","duration":460,"timestamp":2828746079749,"id":563,"parentId":553,"tags":{},"startTime":1753787674888,"traceId":"9380624929474dbc"},{"name":"module-assets","duration":408,"timestamp":2828746080176,"id":564,"parentId":553,"tags":{},"startTime":1753787674888,"traceId":"9380624929474dbc"},{"name":"create-chunk-assets","duration":11298,"timestamp":2828746080609,"id":565,"parentId":553,"tags":{},"startTime":1753787674888,"traceId":"9380624929474dbc"},{"name":"NextJsBuildManifest-generateClientManifest","duration":331,"timestamp":2828746094550,"id":567,"parentId":536,"tags":{},"startTime":1753787674902,"traceId":"9380624929474dbc"},{"name":"NextJsBuildManifest-createassets","duration":538,"timestamp":2828746094373,"id":566,"parentId":536,"tags":{},"startTime":1753787674902,"traceId":"9380624929474dbc"},{"name":"seal","duration":115279,"timestamp":2828745993943,"id":553,"parentId":536,"tags":{},"startTime":1753787674802,"traceId":"9380624929474dbc"},{"name":"webpack-compilation","duration":489570,"timestamp":2828745619820,"id":536,"parentId":533,"tags":{"name":"client"},"startTime":1753787674428,"traceId":"9380624929474dbc"},{"name":"emit","duration":45900,"timestamp":2828746109470,"id":568,"parentId":533,"tags":{},"startTime":1753787674917,"traceId":"9380624929474dbc"},{"name":"webpack-invalidated-client","duration":553937,"timestamp":2828745607272,"id":533,"parentId":3,"tags":{"trigger":""},"startTime":1753787674415,"traceId":"9380624929474dbc"}]
[{"name":"client-success","duration":15,"timestamp":2828746172282,"id":569,"parentId":3,"tags":{},"startTime":1753787674980,"traceId":"9380624929474dbc"},{"name":"handle-request","duration":36227,"timestamp":2828746174385,"id":570,"tags":{"url":"/contact?_rsc=1rc55","isTurbopack":false},"startTime":1753787674982,"traceId":"9380624929474dbc"},{"name":"memory-usage","duration":7,"timestamp":2828746210727,"id":571,"parentId":570,"tags":{"url":"/contact?_rsc=1rc55","memory.rss":"285286400","memory.heapUsed":"249489216","memory.heapTotal":"283987968"},"startTime":1753787675019,"traceId":"9380624929474dbc"},{"name":"client-hmr-latency","duration":612000,"timestamp":2828745608849,"id":572,"parentId":3,"tags":{"updatedModules":[],"page":"/contact","isPageHidden":true},"startTime":1753787675029,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":19397,"timestamp":2828751035305,"id":576,"parentId":575,"tags":{"request":"D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"},"startTime":1753787679843,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":36067,"timestamp":2828751035589,"id":581,"parentId":575,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Capp%5C%5Ccontact%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1753787679843,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":36827,"timestamp":2828751035569,"id":580,"parentId":575,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1753787679843,"traceId":"9380624929474dbc"},{"name":"read-resource","duration":12329,"timestamp":2828751060658,"id":584,"parentId":583,"tags":{},"startTime":1753787679868,"traceId":"9380624929474dbc"},{"name":"postcss-process","duration":143078,"timestamp":2828751073268,"id":586,"parentId":585,"tags":{},"startTime":1753787679881,"traceId":"9380624929474dbc"},{"name":"postcss-loader","duration":145110,"timestamp":2828751073179,"id":585,"parentId":583,"tags":{},"startTime":1753787679881,"traceId":"9380624929474dbc"},{"name":"css-loader","duration":70658,"timestamp":2828751218486,"id":587,"parentId":583,"tags":{"astUsed":"true"},"startTime":1753787680026,"traceId":"9380624929474dbc"},{"name":"build-module-css","duration":234899,"timestamp":2828751060282,"id":583,"parentId":582,"tags":{"name":"D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\app\\globals.css.webpack[javascript/auto]!=!D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[14].oneOf[12].use[2]!D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[14].oneOf[12].use[3]!D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\app\\globals.css","layer":null},"startTime":1753787679868,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":269462,"timestamp":2828751035466,"id":577,"parentId":575,"tags":{"request":"./node_modules/next/dist/client/app-next-dev.js"},"startTime":1753787679843,"traceId":"9380624929474dbc"},{"name":"build-module-css","duration":266835,"timestamp":2828751046910,"id":582,"parentId":574,"tags":{"name":"D:\\mypersonal porfolio which i clone from meer hadi\\animted-Portfolio\\app\\globals.css","layer":"app-pages-browser"},"startTime":1753787679855,"traceId":"9380624929474dbc"},{"name":"build-module","duration":203,"timestamp":2828751319934,"id":588,"parentId":582,"tags":{},"startTime":1753787680128,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":286515,"timestamp":2828751035512,"id":579,"parentId":575,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1753787679843,"traceId":"9380624929474dbc"},{"name":"add-entry","duration":299002,"timestamp":2828751035494,"id":578,"parentId":575,"tags":{"request":"next-flight-client-entry-loader?modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"},"startTime":1753787679843,"traceId":"9380624929474dbc"},{"name":"make","duration":301033,"timestamp":2828751033578,"id":575,"parentId":574,"tags":{},"startTime":1753787679841,"traceId":"9380624929474dbc"},{"name":"chunk-graph","duration":6575,"timestamp":2828751363887,"id":590,"parentId":589,"tags":{},"startTime":1753787680172,"traceId":"9380624929474dbc"},{"name":"optimize-modules","duration":15,"timestamp":2828751370574,"id":592,"parentId":589,"tags":{},"startTime":1753787680178,"traceId":"9380624929474dbc"},{"name":"optimize-chunks","duration":204,"timestamp":2828751370666,"id":593,"parentId":589,"tags":{},"startTime":1753787680178,"traceId":"9380624929474dbc"},{"name":"optimize-tree","duration":18,"timestamp":2828751370937,"id":594,"parentId":589,"tags":{},"startTime":1753787680179,"traceId":"9380624929474dbc"},{"name":"optimize-chunk-modules","duration":17,"timestamp":2828751371021,"id":595,"parentId":589,"tags":{},"startTime":1753787680179,"traceId":"9380624929474dbc"},{"name":"optimize","duration":4656,"timestamp":2828751370538,"id":591,"parentId":589,"tags":{},"startTime":1753787680178,"traceId":"9380624929474dbc"},{"name":"module-hash","duration":1157,"timestamp":2828751379322,"id":596,"parentId":589,"tags":{},"startTime":1753787680187,"traceId":"9380624929474dbc"},{"name":"code-generation","duration":9274,"timestamp":2828751380531,"id":597,"parentId":589,"tags":{},"startTime":1753787680188,"traceId":"9380624929474dbc"},{"name":"hash","duration":15079,"timestamp":2828751393979,"id":598,"parentId":589,"tags":{},"startTime":1753787680202,"traceId":"9380624929474dbc"},{"name":"code-generation-jobs","duration":429,"timestamp":2828751409053,"id":599,"parentId":589,"tags":{},"startTime":1753787680217,"traceId":"9380624929474dbc"},{"name":"module-assets","duration":409,"timestamp":2828751409453,"id":600,"parentId":589,"tags":{},"startTime":1753787680217,"traceId":"9380624929474dbc"},{"name":"create-chunk-assets","duration":22409,"timestamp":2828751409887,"id":601,"parentId":589,"tags":{},"startTime":1753787680218,"traceId":"9380624929474dbc"},{"name":"NextJsBuildManifest-generateClientManifest","duration":459,"timestamp":2828751436024,"id":603,"parentId":574,"tags":{},"startTime":1753787680244,"traceId":"9380624929474dbc"},{"name":"NextJsBuildManifest-createassets","duration":811,"timestamp":2828751435836,"id":602,"parentId":574,"tags":{},"startTime":1753787680244,"traceId":"9380624929474dbc"},{"name":"seal","duration":89568,"timestamp":2828751351672,"id":589,"parentId":574,"tags":{},"startTime":1753787680160,"traceId":"9380624929474dbc"},{"name":"webpack-compilation","duration":412026,"timestamp":2828751029514,"id":574,"parentId":573,"tags":{"name":"client"},"startTime":1753787679837,"traceId":"9380624929474dbc"},{"name":"emit","duration":47849,"timestamp":2828751441635,"id":604,"parentId":573,"tags":{},"startTime":1753787680249,"traceId":"9380624929474dbc"},{"name":"webpack-invalidated-client","duration":487743,"timestamp":2828751007905,"id":573,"parentId":3,"tags":{"trigger":"public/placeholder.jpg"},"startTime":1753787679816,"traceId":"9380624929474dbc"}]
