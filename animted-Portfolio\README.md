# Animated Portfolio Website

This is a modern, animated portfolio website built with **Next.js**, **Framer Motion**, and **GSAP**. The project showcases advanced UI/UX, smooth animations, and a beautiful, interactive design. I created this for people who want to take inspiration or clone it for their own needs.

## Features

- **Framer Motion** for smooth, declarative React animations
- **GSAP (GreenSock Animation Platform)** for scroll-based and advanced timeline animations
- **Glassmorphism** and modern UI design
- **Responsive** and mobile-friendly layout
- **Animated sections**: Hero, About, Skills, Projects, Testimonials, Contact
- **Custom cursor and parallax effects**
- **Beautiful tab switchers and animated indicators**
- **Interactive project cards with 3D tilt**

## Getting Started

### 1. <PERSON>lone the Repository

```bash
git clone https://github.com/your-username/your-portfolio-repo.git
cd your-portfolio-repo/my-app
```

### 2. Install Dependencies

This project uses [pnpm](https://pnpm.io/) for fast installs, but you can use npm or yarn as well.

```bash
pnpm install
# or
npm install
# or
yarn install
```

### 3. Run the Development Server

```bash
pnpm dev
# or
npm run dev
# or
yarn dev
```

Open [http://localhost:3000](http://localhost:3000) to view the site.

## Animation Libraries Used

- [**Framer Motion**](https://www.framer.com/motion/) – For page transitions, section reveals, and interactive UI animations.
- [**GSAP**](https://greensock.com/gsap/) – For scroll-based animations, parallax effects, and timeline-based motion.

## Folder Structure

- `app/` – Main Next.js app directory
- `components/` – Reusable UI components (including custom animated components)
- `public/` – Static assets (images, profile picture, etc.)
- `styles/` – Global styles and Tailwind CSS

## Customization

Feel free to:
- Change the content, images, and sections to fit your needs
- Tweak the animations or add your own
- Use this as a starter for your own creative portfolio

## Inspiration & License

I built this project for the community, so you can take inspiration, learn from the code, or clone it for your own portfolio. No attribution required, but a star or mention is appreciated!

---

<<<<<<< HEAD
**Happy coding!**
=======
<<<<<<< HEAD
**Happy coding!**
=======
**Happy coding!** 
>>>>>>> 104e240 (Initial commit: Animated Portfolio Website)
>>>>>>> 68c47d6 (Initial commit)
