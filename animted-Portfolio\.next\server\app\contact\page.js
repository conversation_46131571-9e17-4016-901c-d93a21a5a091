/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/contact/page";
exports.ids = ["app/contact/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fcontact%2Fpage&page=%2Fcontact%2Fpage&appPaths=%2Fcontact%2Fpage&pagePath=private-next-app-dir%2Fcontact%2Fpage.tsx&appDir=D%3A%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5Canimted-Portfolio%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5Canimted-Portfolio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fcontact%2Fpage&page=%2Fcontact%2Fpage&appPaths=%2Fcontact%2Fpage&pagePath=private-next-app-dir%2Fcontact%2Fpage.tsx&appDir=D%3A%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5Canimted-Portfolio%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5Canimted-Portfolio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9100\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'contact',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/contact/page.tsx */ \"(rsc)/./app/contact/page.tsx\")), \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\contact\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\contact\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/contact/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/contact/page\",\n        pathname: \"/contact\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fcontact%2Fpage&page=%2Fcontact%2Fpage&appPaths=%2Fcontact%2Fpage&pagePath=private-next-app-dir%2Fcontact%2Fpage.tsx&appDir=D%3A%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5Canimted-Portfolio%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5Canimted-Portfolio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Capp%5C%5Ccontact%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Capp%5C%5Ccontact%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/contact/page.tsx */ \"(ssr)/./app/contact/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNteXBlcnNvbmFsJTIwcG9yZm9saW8lMjB3aGljaCUyMGklMjBjbG9uZSUyMGZyb20lMjBtZWVyJTIwaGFkaSU1QyU1Q2FuaW10ZWQtUG9ydGZvbGlvJTVDJTVDYXBwJTVDJTVDY29udGFjdCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3SkFBb0kiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9teS12MC1wcm9qZWN0Lz80ZjVlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcbXlwZXJzb25hbCBwb3Jmb2xpbyB3aGljaCBpIGNsb25lIGZyb20gbWVlciBoYWRpXFxcXGFuaW10ZWQtUG9ydGZvbGlvXFxcXGFwcFxcXFxjb250YWN0XFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Capp%5C%5Ccontact%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5C%5Canimted-Portfolio%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./app/contact/page.tsx":
/*!******************************!*\
  !*** ./app/contact/page.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ContactPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Github_Mail_MapPin_Phone_Send_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Github,Mail,MapPin,Phone,Send!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Github_Mail_MapPin_Phone_Send_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Github,Mail,MapPin,Phone,Send!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Github_Mail_MapPin_Phone_Send_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Github,Mail,MapPin,Phone,Send!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Github_Mail_MapPin_Phone_Send_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Github,Mail,MapPin,Phone,Send!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Github_Mail_MapPin_Phone_Send_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Github,Mail,MapPin,Phone,Send!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Github_Mail_MapPin_Phone_Send_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Github,Mail,MapPin,Phone,Send!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction ContactPage() {\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        email: \"\",\n        subject: \"\",\n        message: \"\"\n    });\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSubmitted, setIsSubmitted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setIsSubmitting(true);\n        // Simulate form submission\n        await new Promise((resolve)=>setTimeout(resolve, 2000));\n        setIsSubmitting(false);\n        setIsSubmitted(true);\n        // Reset form after 3 seconds\n        setTimeout(()=>{\n            setIsSubmitted(false);\n            setFormData({\n                name: \"\",\n                email: \"\",\n                subject: \"\",\n                message: \"\"\n            });\n        }, 3000);\n    };\n    const handleChange = (e)=>{\n        setFormData({\n            ...formData,\n            [e.target.name]: e.target.value\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 text-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 overflow-hidden pointer-events-none\",\n                children: [\n                    ...Array(20)\n                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        className: \"absolute w-1 h-1 bg-white rounded-full opacity-30\",\n                        style: {\n                            left: `${Math.random() * 100}%`,\n                            top: `${Math.random() * 100}%`\n                        },\n                        animate: {\n                            y: [\n                                -20,\n                                20\n                            ],\n                            x: [\n                                -10,\n                                10\n                            ],\n                            opacity: [\n                                0.3,\n                                0.8,\n                                0.3\n                            ]\n                        },\n                        transition: {\n                            duration: 3 + Math.random() * 2,\n                            repeat: Number.POSITIVE_INFINITY,\n                            delay: Math.random() * 2\n                        }\n                    }, i, false, {\n                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\contact\\\\page.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\contact\\\\page.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"p-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            href: \"/\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                className: \"flex items-center gap-2 text-white hover:text-purple-300 transition-colors cursor-pointer\",\n                                whileHover: {\n                                    x: -5\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Github_Mail_MapPin_Phone_Send_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        size: 24\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\contact\\\\page.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-bold\",\n                                        children: \"Back to Portfolio\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\contact\\\\page.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\contact\\\\page.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\contact\\\\page.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\contact\\\\page.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-6xl mx-auto pt-32 pb-32 px-4 md:px-8 lg:px-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.h1, {\n                                className: \"text-5xl md:text-7xl font-black tracking-tighter mb-8 text-center\",\n                                initial: {\n                                    opacity: 0,\n                                    y: 50\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8\n                                },\n                                children: \"GET IN TOUCH\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\contact\\\\page.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.p, {\n                                className: \"text-xl text-center text-purple-200 mb-16 max-w-2xl mx-auto\",\n                                initial: {\n                                    opacity: 0,\n                                    y: 30\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    duration: 0.8,\n                                    delay: 0.2\n                                },\n                                children: \"Have a project in mind? Let's discuss how we can bring your vision to life.\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\contact\\\\page.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid lg:grid-cols-2 gap-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                        className: \"space-y-8\",\n                                        initial: {\n                                            opacity: 0,\n                                            x: -50\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        transition: {\n                                            duration: 0.8,\n                                            delay: 0.4\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-12\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-32 h-32 mx-auto lg:mx-0 mb-6 bg-gradient-to-br from-purple-600 to-blue-600 rounded-full flex items-center justify-center overflow-hidden\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: \"/hijabi-girl-aesthetic.jpg\",\n                                                            alt: \"Iqra Urooj\",\n                                                            className: \"w-28 h-28 object-cover rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\contact\\\\page.tsx\",\n                                                            lineNumber: 114,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 113,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-3xl font-black mb-2\",\n                                                        children: \"Iqra Urooj\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 116,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-purple-300 text-lg\",\n                                                        children: \"Full Stack Developer & Designer\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 117,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\contact\\\\page.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                                        className: \"flex items-center gap-4 p-4 bg-black/30 rounded-lg backdrop-blur-sm\",\n                                                        whileHover: {\n                                                            scale: 1.02\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Github_Mail_MapPin_Phone_Send_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                className: \"text-purple-400\",\n                                                                size: 24\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\contact\\\\page.tsx\",\n                                                                lineNumber: 125,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-semibold\",\n                                                                        children: \"Email\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\contact\\\\page.tsx\",\n                                                                        lineNumber: 127,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-purple-200\",\n                                                                        children: \"<EMAIL>\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\contact\\\\page.tsx\",\n                                                                        lineNumber: 128,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\contact\\\\page.tsx\",\n                                                                lineNumber: 126,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 121,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                                        className: \"flex items-center gap-4 p-4 bg-black/30 rounded-lg backdrop-blur-sm\",\n                                                        whileHover: {\n                                                            scale: 1.02\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Github_Mail_MapPin_Phone_Send_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                className: \"text-purple-400\",\n                                                                size: 24\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\contact\\\\page.tsx\",\n                                                                lineNumber: 136,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-semibold\",\n                                                                        children: \"Phone\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\contact\\\\page.tsx\",\n                                                                        lineNumber: 138,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-purple-200\",\n                                                                        children: \"03429813925\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\contact\\\\page.tsx\",\n                                                                        lineNumber: 139,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\contact\\\\page.tsx\",\n                                                                lineNumber: 137,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 132,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                                        className: \"flex items-center gap-4 p-4 bg-black/30 rounded-lg backdrop-blur-sm\",\n                                                        whileHover: {\n                                                            scale: 1.02\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Github_Mail_MapPin_Phone_Send_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                className: \"text-purple-400\",\n                                                                size: 24\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\contact\\\\page.tsx\",\n                                                                lineNumber: 147,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"font-semibold\",\n                                                                        children: \"Location\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\contact\\\\page.tsx\",\n                                                                        lineNumber: 149,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-purple-200\",\n                                                                        children: \"Dera Ismail Khan (KPK) Pak\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\contact\\\\page.tsx\",\n                                                                        lineNumber: 150,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\contact\\\\page.tsx\",\n                                                                lineNumber: 148,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 143,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\contact\\\\page.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"pt-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-bold mb-4\",\n                                                        children: \"Follow Me\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 156,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.a, {\n                                                            href: \"https://github.com/uroojiqra58gmailcom\",\n                                                            target: \"_blank\",\n                                                            rel: \"noopener noreferrer\",\n                                                            className: \"p-3 bg-black/30 rounded-lg backdrop-blur-sm hover:bg-purple-600 transition-colors\",\n                                                            whileHover: {\n                                                                scale: 1.1\n                                                            },\n                                                            whileTap: {\n                                                                scale: 0.95\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Github_Mail_MapPin_Phone_Send_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                size: 24\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\contact\\\\page.tsx\",\n                                                                lineNumber: 166,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\contact\\\\page.tsx\",\n                                                            lineNumber: 158,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 157,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\contact\\\\page.tsx\",\n                                                lineNumber: 155,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\contact\\\\page.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                        className: \"bg-black/30 backdrop-blur-sm rounded-2xl p-8\",\n                                        initial: {\n                                            opacity: 0,\n                                            x: 50\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        transition: {\n                                            duration: 0.8,\n                                            delay: 0.6\n                                        },\n                                        children: isSubmitted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                            className: \"text-center py-16\",\n                                            initial: {\n                                                opacity: 0,\n                                                scale: 0.8\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                scale: 1\n                                            },\n                                            transition: {\n                                                duration: 0.5\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Github_Mail_MapPin_Phone_Send_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"text-white\",\n                                                        size: 24\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 187,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\contact\\\\page.tsx\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-2xl font-bold mb-2\",\n                                                    children: \"Message Sent!\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\contact\\\\page.tsx\",\n                                                    lineNumber: 189,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-purple-200\",\n                                                    children: \"Thank you for reaching out. I'll get back to you soon!\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\contact\\\\page.tsx\",\n                                                    lineNumber: 190,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\contact\\\\page.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            onSubmit: handleSubmit,\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid md:grid-cols-2 gap-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"name\",\n                                                                    className: \"block text-sm font-semibold mb-2\",\n                                                                    children: \"Name *\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\contact\\\\page.tsx\",\n                                                                    lineNumber: 196,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    id: \"name\",\n                                                                    name: \"name\",\n                                                                    value: formData.name,\n                                                                    onChange: handleChange,\n                                                                    required: true,\n                                                                    className: \"w-full px-4 py-3 bg-black/50 border border-purple-500/30 rounded-lg focus:border-purple-500 focus:outline-none transition-colors text-white placeholder-gray-400\",\n                                                                    placeholder: \"Your name\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\contact\\\\page.tsx\",\n                                                                    lineNumber: 199,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\contact\\\\page.tsx\",\n                                                            lineNumber: 195,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"email\",\n                                                                    className: \"block text-sm font-semibold mb-2\",\n                                                                    children: \"Email *\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\contact\\\\page.tsx\",\n                                                                    lineNumber: 211,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"email\",\n                                                                    id: \"email\",\n                                                                    name: \"email\",\n                                                                    value: formData.email,\n                                                                    onChange: handleChange,\n                                                                    required: true,\n                                                                    className: \"w-full px-4 py-3 bg-black/50 border border-purple-500/30 rounded-lg focus:border-purple-500 focus:outline-none transition-colors text-white placeholder-gray-400\",\n                                                                    placeholder: \"<EMAIL>\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\contact\\\\page.tsx\",\n                                                                    lineNumber: 214,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\contact\\\\page.tsx\",\n                                                            lineNumber: 210,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\contact\\\\page.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"subject\",\n                                                            className: \"block text-sm font-semibold mb-2\",\n                                                            children: \"Subject *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\contact\\\\page.tsx\",\n                                                            lineNumber: 228,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            id: \"subject\",\n                                                            name: \"subject\",\n                                                            value: formData.subject,\n                                                            onChange: handleChange,\n                                                            required: true,\n                                                            className: \"w-full px-4 py-3 bg-black/50 border border-purple-500/30 rounded-lg focus:border-purple-500 focus:outline-none transition-colors text-white placeholder-gray-400\",\n                                                            placeholder: \"Project inquiry\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\contact\\\\page.tsx\",\n                                                            lineNumber: 231,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\contact\\\\page.tsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"message\",\n                                                            className: \"block text-sm font-semibold mb-2\",\n                                                            children: \"Message *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\contact\\\\page.tsx\",\n                                                            lineNumber: 244,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            id: \"message\",\n                                                            name: \"message\",\n                                                            value: formData.message,\n                                                            onChange: handleChange,\n                                                            required: true,\n                                                            rows: 6,\n                                                            className: \"w-full px-4 py-3 bg-black/50 border border-purple-500/30 rounded-lg focus:border-purple-500 focus:outline-none transition-colors text-white placeholder-gray-400 resize-none\",\n                                                            placeholder: \"Tell me about your project...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\contact\\\\page.tsx\",\n                                                            lineNumber: 247,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\contact\\\\page.tsx\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.button, {\n                                                    type: \"submit\",\n                                                    disabled: isSubmitting,\n                                                    className: \"w-full py-4 bg-gradient-to-r from-purple-600 to-blue-600 text-white font-bold text-lg rounded-lg hover:from-purple-700 hover:to-blue-700 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2\",\n                                                    whileHover: {\n                                                        scale: isSubmitting ? 1 : 1.02\n                                                    },\n                                                    whileTap: {\n                                                        scale: isSubmitting ? 1 : 0.98\n                                                    },\n                                                    children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\contact\\\\page.tsx\",\n                                                                lineNumber: 268,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Sending...\"\n                                                        ]\n                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Github_Mail_MapPin_Phone_Send_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                size: 20\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\contact\\\\page.tsx\",\n                                                                lineNumber: 273,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Send Message\"\n                                                        ]\n                                                    }, void 0, true)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\contact\\\\page.tsx\",\n                                                    lineNumber: 259,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\contact\\\\page.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\contact\\\\page.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\contact\\\\page.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\contact\\\\page.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\contact\\\\page.tsx\",\n                lineNumber: 71,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\contact\\\\page.tsx\",\n        lineNumber: 46,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/contact/page.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"8aba33dda0fa\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9teS12MC1wcm9qZWN0Ly4vYXBwL2dsb2JhbHMuY3NzP2RkYTciXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI4YWJhMzNkZGEwZmFcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/contact/page.tsx":
/*!******************************!*\
  !*** ./app/contact/page.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\mypersonal porfolio which i clone from meer hadi\animted-Portfolio\app\contact\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"Your Name - Portfolio\",\n    description: \"Portfolio of John Doe - Creative Developer & Designer\",\n    generator: \"v0.dev\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"dark\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                    href: \"https://fonts.googleapis.com/css2?family=Montserrat:wght@700;900&display=swap\",\n                    rel: \"stylesheet\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\layout.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\layout.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className)} min-h-screen flex flex-col`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"flex-1\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\layout.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\layout.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\mypersonal porfolio which i clone from meer hadi\\\\animted-Portfolio\\\\app\\\\layout.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUtNQTtBQUZnQjtBQUlmLE1BQU1DLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7SUFDWEMsV0FBVztBQUNmLEVBQUM7QUFFYyxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztRQUFLQyxXQUFVOzswQkFDeEIsOERBQUNDOzBCQUNDLDRFQUFDQztvQkFBS0MsTUFBSztvQkFBZ0ZDLEtBQUk7Ozs7Ozs7Ozs7OzBCQUVqRyw4REFBQ0M7Z0JBQUtMLFdBQVcsQ0FBQyxFQUFFVCwySkFBZSxDQUFDLDJCQUEyQixDQUFDOzBCQUM5RCw0RUFBQ2U7b0JBQUtOLFdBQVU7OEJBQVVIOzs7Ozs7Ozs7Ozs7Ozs7OztBQUlsQyIsInNvdXJjZXMiOlsid2VicGFjazovL215LXYwLXByb2plY3QvLi9hcHAvbGF5b3V0LnRzeD85OTg4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIFJlYWN0IGZyb20gXCJyZWFjdFwiXHJcbmltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tIFwibmV4dFwiXHJcbmltcG9ydCB7IEludGVyIH0gZnJvbSBcIm5leHQvZm9udC9nb29nbGVcIlxyXG5pbXBvcnQgXCIuL2dsb2JhbHMuY3NzXCJcclxuXHJcbmNvbnN0IGludGVyID0gSW50ZXIoeyBzdWJzZXRzOiBbXCJsYXRpblwiXSB9KVxyXG5cclxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcclxuICB0aXRsZTogXCJZb3VyIE5hbWUgLSBQb3J0Zm9saW9cIixcclxuICBkZXNjcmlwdGlvbjogXCJQb3J0Zm9saW8gb2YgSm9obiBEb2UgLSBDcmVhdGl2ZSBEZXZlbG9wZXIgJiBEZXNpZ25lclwiLFxyXG4gICAgZ2VuZXJhdG9yOiAndjAuZGV2J1xyXG59XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcclxuICBjaGlsZHJlbixcclxufToge1xyXG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGVcclxufSkge1xyXG4gIHJldHVybiAoXHJcbiAgICA8aHRtbCBsYW5nPVwiZW5cIiBjbGFzc05hbWU9XCJkYXJrXCI+XHJcbiAgICAgIDxoZWFkPlxyXG4gICAgICAgIDxsaW5rIGhyZWY9XCJodHRwczovL2ZvbnRzLmdvb2dsZWFwaXMuY29tL2NzczI/ZmFtaWx5PU1vbnRzZXJyYXQ6d2dodEA3MDA7OTAwJmRpc3BsYXk9c3dhcFwiIHJlbD1cInN0eWxlc2hlZXRcIiAvPlxyXG4gICAgICA8L2hlYWQ+XHJcbiAgICAgIDxib2R5IGNsYXNzTmFtZT17YCR7aW50ZXIuY2xhc3NOYW1lfSBtaW4taC1zY3JlZW4gZmxleCBmbGV4LWNvbGB9PlxyXG4gICAgICAgIDxtYWluIGNsYXNzTmFtZT1cImZsZXgtMVwiPntjaGlsZHJlbn08L21haW4+XHJcbiAgICAgIDwvYm9keT5cclxuICAgIDwvaHRtbD5cclxuICApXHJcbn1cclxuIl0sIm5hbWVzIjpbImludGVyIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiZ2VuZXJhdG9yIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJjbGFzc05hbWUiLCJoZWFkIiwibGluayIsImhyZWYiLCJyZWwiLCJib2R5IiwibWFpbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/framer-motion","vendor-chunks/motion-dom","vendor-chunks/lucide-react","vendor-chunks/motion-utils","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fcontact%2Fpage&page=%2Fcontact%2Fpage&appPaths=%2Fcontact%2Fpage&pagePath=private-next-app-dir%2Fcontact%2Fpage.tsx&appDir=D%3A%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5Canimted-Portfolio%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cmypersonal%20porfolio%20which%20i%20clone%20from%20meer%20hadi%5Canimted-Portfolio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();